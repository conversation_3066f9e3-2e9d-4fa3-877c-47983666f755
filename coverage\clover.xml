<?xml version="1.0" encoding="UTF-8"?>
<coverage generated="1748859183085" clover="3.2.0">
  <project timestamp="1748859183085" name="All files">
    <metrics statements="266" coveredstatements="149" conditionals="95" coveredconditionals="21" methods="60" coveredmethods="39" elements="421" coveredelements="209" complexity="0" loc="266" ncloc="266" packages="3" files="4" classes="4"/>
    <package name="config">
      <metrics statements="29" coveredstatements="13" conditionals="14" coveredconditionals="4" methods="9" coveredmethods="3"/>
      <file name="ConfigManager.js" path="D:\TW\src\config\ConfigManager.js">
        <metrics statements="29" coveredstatements="13" conditionals="14" coveredconditionals="4" methods="9" coveredmethods="3"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="7" count="1" type="stmt"/>
        <line num="8" count="1" type="stmt"/>
        <line num="9" count="1" type="stmt"/>
        <line num="13" count="1" type="stmt"/>
        <line num="14" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="15" count="1" type="stmt"/>
        <line num="16" count="1" type="stmt"/>
        <line num="17" count="1" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="26" count="2" type="cond" truecount="3" falsecount="0"/>
        <line num="30" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="36" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="46" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="61" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="62" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="72" count="1" type="stmt"/>
      </file>
    </package>
    <package name="database">
      <metrics statements="117" coveredstatements="101" conditionals="16" coveredconditionals="10" methods="26" coveredmethods="26"/>
      <file name="DatabaseManager.js" path="D:\TW\src\database\DatabaseManager.js">
        <metrics statements="117" coveredstatements="101" conditionals="16" coveredconditionals="10" methods="26" coveredmethods="26"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="6" count="1" type="stmt"/>
        <line num="10" count="1" type="stmt"/>
        <line num="11" count="1" type="stmt"/>
        <line num="12" count="1" type="stmt"/>
        <line num="16" count="1" type="stmt"/>
        <line num="17" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="18" count="1" type="stmt"/>
        <line num="23" count="1" type="stmt"/>
        <line num="34" count="1" type="stmt"/>
        <line num="40" count="1" type="stmt"/>
        <line num="44" count="13" type="stmt"/>
        <line num="46" count="13" type="stmt"/>
        <line num="47" count="13" type="stmt"/>
        <line num="48" count="13" type="stmt"/>
        <line num="49" count="13" type="stmt"/>
        <line num="50" count="13" type="stmt"/>
        <line num="51" count="13" type="stmt"/>
        <line num="52" count="13" type="stmt"/>
        <line num="56" count="13" type="stmt"/>
        <line num="57" count="13" type="stmt"/>
        <line num="58" count="13" type="stmt"/>
        <line num="59" count="13" type="stmt"/>
        <line num="60" count="13" type="stmt"/>
        <line num="61" count="13" type="stmt"/>
        <line num="62" count="13" type="stmt"/>
        <line num="63" count="13" type="stmt"/>
        <line num="67" count="13" type="stmt"/>
        <line num="68" count="13" type="stmt"/>
        <line num="69" count="13" type="stmt"/>
        <line num="70" count="13" type="stmt"/>
        <line num="71" count="13" type="stmt"/>
        <line num="72" count="13" type="stmt"/>
        <line num="73" count="13" type="stmt"/>
        <line num="77" count="13" type="stmt"/>
        <line num="78" count="13" type="stmt"/>
        <line num="79" count="13" type="stmt"/>
        <line num="80" count="13" type="stmt"/>
        <line num="81" count="13" type="stmt"/>
        <line num="82" count="13" type="stmt"/>
        <line num="83" count="13" type="stmt"/>
        <line num="87" count="13" type="stmt"/>
        <line num="88" count="13" type="stmt"/>
        <line num="89" count="13" type="stmt"/>
        <line num="90" count="13" type="stmt"/>
        <line num="91" count="13" type="stmt"/>
        <line num="92" count="13" type="stmt"/>
        <line num="95" count="13" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="107" count="12" type="stmt"/>
        <line num="108" count="12" type="stmt"/>
        <line num="109" count="12" type="stmt"/>
        <line num="110" count="12" type="stmt"/>
        <line num="111" count="12" type="stmt"/>
        <line num="112" count="12" type="stmt"/>
        <line num="113" count="12" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="125" count="12" type="stmt"/>
        <line num="126" count="12" type="stmt"/>
        <line num="131" count="10" type="stmt"/>
        <line num="132" count="10" type="stmt"/>
        <line num="138" count="9" type="stmt"/>
        <line num="140" count="1" type="stmt"/>
        <line num="149" count="1" type="stmt"/>
        <line num="150" count="1" type="stmt"/>
        <line num="157" count="1" type="stmt"/>
        <line num="159" count="0" type="stmt"/>
        <line num="169" count="6" type="stmt"/>
        <line num="170" count="6" type="stmt"/>
        <line num="179" count="6" type="stmt"/>
        <line num="181" count="0" type="stmt"/>
        <line num="190" count="1" type="stmt"/>
        <line num="191" count="1" type="stmt"/>
        <line num="197" count="1" type="stmt"/>
        <line num="199" count="0" type="stmt"/>
        <line num="209" count="1" type="stmt"/>
        <line num="210" count="1" type="stmt"/>
        <line num="216" count="1" type="stmt"/>
        <line num="218" count="0" type="stmt"/>
        <line num="227" count="2" type="stmt"/>
        <line num="228" count="2" type="stmt"/>
        <line num="234" count="1" type="stmt"/>
        <line num="236" count="1" type="stmt"/>
        <line num="246" count="1" type="stmt"/>
        <line num="247" count="1" type="stmt"/>
        <line num="253" count="1" type="stmt"/>
        <line num="255" count="0" type="stmt"/>
        <line num="265" count="1" type="stmt"/>
        <line num="266" count="1" type="stmt"/>
        <line num="267" count="1" type="stmt"/>
        <line num="269" count="0" type="stmt"/>
        <line num="278" count="3" type="stmt"/>
        <line num="279" count="3" type="stmt"/>
        <line num="283" count="0" type="stmt"/>
        <line num="292" count="4" type="stmt"/>
        <line num="293" count="4" type="stmt"/>
        <line num="297" count="0" type="stmt"/>
        <line num="306" count="1" type="stmt"/>
        <line num="307" count="1" type="stmt"/>
        <line num="313" count="0" type="stmt"/>
        <line num="322" count="1" type="stmt"/>
        <line num="323" count="1" type="stmt"/>
        <line num="329" count="0" type="stmt"/>
        <line num="338" count="1" type="stmt"/>
        <line num="339" count="1" type="stmt"/>
        <line num="345" count="0" type="stmt"/>
        <line num="355" count="1" type="stmt"/>
        <line num="356" count="1" type="stmt"/>
        <line num="357" count="1" type="stmt"/>
        <line num="359" count="0" type="stmt"/>
        <line num="369" count="1" type="stmt"/>
      </file>
    </package>
    <package name="utils">
      <metrics statements="120" coveredstatements="35" conditionals="65" coveredconditionals="7" methods="25" coveredmethods="10"/>
      <file name="ErrorHandler.js" path="D:\TW\src\utils\ErrorHandler.js">
        <metrics statements="83" coveredstatements="9" conditionals="44" coveredconditionals="0" methods="10" coveredmethods="2"/>
        <line num="1" count="1" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="16" count="1" type="stmt"/>
        <line num="23" count="1" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="43" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="50" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="51" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="58" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="59" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="82" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="84" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="109" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="111" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="118" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="119" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="120" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="121" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="123" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="127" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="128" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="135" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="141" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="148" count="0" type="stmt"/>
        <line num="149" count="0" type="stmt"/>
        <line num="154" count="2" type="stmt"/>
        <line num="155" count="2" type="stmt"/>
        <line num="156" count="2" type="stmt"/>
        <line num="157" count="2" type="stmt"/>
        <line num="162" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="163" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="165" count="0" type="stmt"/>
        <line num="166" count="0" type="stmt"/>
        <line num="168" count="0" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
        <line num="171" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="172" count="0" type="stmt"/>
        <line num="173" count="0" type="stmt"/>
        <line num="180" count="0" type="stmt"/>
        <line num="181" count="0" type="stmt"/>
        <line num="187" count="1" type="stmt"/>
      </file>
      <file name="Logger.js" path="D:\TW\src\utils\Logger.js">
        <metrics statements="37" coveredstatements="26" conditionals="21" coveredconditionals="7" methods="15" coveredmethods="8"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="8" count="1" type="stmt"/>
        <line num="9" count="1" type="stmt"/>
        <line num="10" count="1" type="stmt"/>
        <line num="11" count="1" type="stmt"/>
        <line num="12" count="1" type="stmt"/>
        <line num="13" count="1" type="stmt"/>
        <line num="17" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="18" count="0" type="stmt"/>
        <line num="24" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="25" count="0" type="stmt"/>
        <line num="27" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="28" count="0" type="stmt"/>
        <line num="33" count="1" type="stmt"/>
        <line num="35" count="1" type="stmt"/>
        <line num="65" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="66" count="1" type="stmt"/>
        <line num="76" count="47" type="stmt"/>
        <line num="78" count="47" type="cond" truecount="1" falsecount="1"/>
        <line num="79" count="47" type="stmt"/>
        <line num="84" count="46" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="96" count="1" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="129" count="47" type="stmt"/>
        <line num="137" count="47" type="stmt"/>
        <line num="138" count="47" type="cond" truecount="1" falsecount="1"/>
        <line num="139" count="0" type="stmt"/>
        <line num="145" count="1" type="stmt"/>
      </file>
    </package>
  </project>
</coverage>
