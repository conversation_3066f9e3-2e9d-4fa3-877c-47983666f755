
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for config/ConfigManager.js</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../prettify.css" />
    <link rel="stylesheet" href="../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../index.html">All files</a> / <a href="index.html">config</a> ConfigManager.js</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>0/83</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>0/137</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>0/12</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>0/83</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a>
<a name='L76'></a><a href='#L76'>76</a>
<a name='L77'></a><a href='#L77'>77</a>
<a name='L78'></a><a href='#L78'>78</a>
<a name='L79'></a><a href='#L79'>79</a>
<a name='L80'></a><a href='#L80'>80</a>
<a name='L81'></a><a href='#L81'>81</a>
<a name='L82'></a><a href='#L82'>82</a>
<a name='L83'></a><a href='#L83'>83</a>
<a name='L84'></a><a href='#L84'>84</a>
<a name='L85'></a><a href='#L85'>85</a>
<a name='L86'></a><a href='#L86'>86</a>
<a name='L87'></a><a href='#L87'>87</a>
<a name='L88'></a><a href='#L88'>88</a>
<a name='L89'></a><a href='#L89'>89</a>
<a name='L90'></a><a href='#L90'>90</a>
<a name='L91'></a><a href='#L91'>91</a>
<a name='L92'></a><a href='#L92'>92</a>
<a name='L93'></a><a href='#L93'>93</a>
<a name='L94'></a><a href='#L94'>94</a>
<a name='L95'></a><a href='#L95'>95</a>
<a name='L96'></a><a href='#L96'>96</a>
<a name='L97'></a><a href='#L97'>97</a>
<a name='L98'></a><a href='#L98'>98</a>
<a name='L99'></a><a href='#L99'>99</a>
<a name='L100'></a><a href='#L100'>100</a>
<a name='L101'></a><a href='#L101'>101</a>
<a name='L102'></a><a href='#L102'>102</a>
<a name='L103'></a><a href='#L103'>103</a>
<a name='L104'></a><a href='#L104'>104</a>
<a name='L105'></a><a href='#L105'>105</a>
<a name='L106'></a><a href='#L106'>106</a>
<a name='L107'></a><a href='#L107'>107</a>
<a name='L108'></a><a href='#L108'>108</a>
<a name='L109'></a><a href='#L109'>109</a>
<a name='L110'></a><a href='#L110'>110</a>
<a name='L111'></a><a href='#L111'>111</a>
<a name='L112'></a><a href='#L112'>112</a>
<a name='L113'></a><a href='#L113'>113</a>
<a name='L114'></a><a href='#L114'>114</a>
<a name='L115'></a><a href='#L115'>115</a>
<a name='L116'></a><a href='#L116'>116</a>
<a name='L117'></a><a href='#L117'>117</a>
<a name='L118'></a><a href='#L118'>118</a>
<a name='L119'></a><a href='#L119'>119</a>
<a name='L120'></a><a href='#L120'>120</a>
<a name='L121'></a><a href='#L121'>121</a>
<a name='L122'></a><a href='#L122'>122</a>
<a name='L123'></a><a href='#L123'>123</a>
<a name='L124'></a><a href='#L124'>124</a>
<a name='L125'></a><a href='#L125'>125</a>
<a name='L126'></a><a href='#L126'>126</a>
<a name='L127'></a><a href='#L127'>127</a>
<a name='L128'></a><a href='#L128'>128</a>
<a name='L129'></a><a href='#L129'>129</a>
<a name='L130'></a><a href='#L130'>130</a>
<a name='L131'></a><a href='#L131'>131</a>
<a name='L132'></a><a href='#L132'>132</a>
<a name='L133'></a><a href='#L133'>133</a>
<a name='L134'></a><a href='#L134'>134</a>
<a name='L135'></a><a href='#L135'>135</a>
<a name='L136'></a><a href='#L136'>136</a>
<a name='L137'></a><a href='#L137'>137</a>
<a name='L138'></a><a href='#L138'>138</a>
<a name='L139'></a><a href='#L139'>139</a>
<a name='L140'></a><a href='#L140'>140</a>
<a name='L141'></a><a href='#L141'>141</a>
<a name='L142'></a><a href='#L142'>142</a>
<a name='L143'></a><a href='#L143'>143</a>
<a name='L144'></a><a href='#L144'>144</a>
<a name='L145'></a><a href='#L145'>145</a>
<a name='L146'></a><a href='#L146'>146</a>
<a name='L147'></a><a href='#L147'>147</a>
<a name='L148'></a><a href='#L148'>148</a>
<a name='L149'></a><a href='#L149'>149</a>
<a name='L150'></a><a href='#L150'>150</a>
<a name='L151'></a><a href='#L151'>151</a>
<a name='L152'></a><a href='#L152'>152</a>
<a name='L153'></a><a href='#L153'>153</a>
<a name='L154'></a><a href='#L154'>154</a>
<a name='L155'></a><a href='#L155'>155</a>
<a name='L156'></a><a href='#L156'>156</a>
<a name='L157'></a><a href='#L157'>157</a>
<a name='L158'></a><a href='#L158'>158</a>
<a name='L159'></a><a href='#L159'>159</a>
<a name='L160'></a><a href='#L160'>160</a>
<a name='L161'></a><a href='#L161'>161</a>
<a name='L162'></a><a href='#L162'>162</a>
<a name='L163'></a><a href='#L163'>163</a>
<a name='L164'></a><a href='#L164'>164</a>
<a name='L165'></a><a href='#L165'>165</a>
<a name='L166'></a><a href='#L166'>166</a>
<a name='L167'></a><a href='#L167'>167</a>
<a name='L168'></a><a href='#L168'>168</a>
<a name='L169'></a><a href='#L169'>169</a>
<a name='L170'></a><a href='#L170'>170</a>
<a name='L171'></a><a href='#L171'>171</a>
<a name='L172'></a><a href='#L172'>172</a>
<a name='L173'></a><a href='#L173'>173</a>
<a name='L174'></a><a href='#L174'>174</a>
<a name='L175'></a><a href='#L175'>175</a>
<a name='L176'></a><a href='#L176'>176</a>
<a name='L177'></a><a href='#L177'>177</a>
<a name='L178'></a><a href='#L178'>178</a>
<a name='L179'></a><a href='#L179'>179</a>
<a name='L180'></a><a href='#L180'>180</a>
<a name='L181'></a><a href='#L181'>181</a>
<a name='L182'></a><a href='#L182'>182</a>
<a name='L183'></a><a href='#L183'>183</a>
<a name='L184'></a><a href='#L184'>184</a>
<a name='L185'></a><a href='#L185'>185</a>
<a name='L186'></a><a href='#L186'>186</a>
<a name='L187'></a><a href='#L187'>187</a>
<a name='L188'></a><a href='#L188'>188</a>
<a name='L189'></a><a href='#L189'>189</a>
<a name='L190'></a><a href='#L190'>190</a>
<a name='L191'></a><a href='#L191'>191</a>
<a name='L192'></a><a href='#L192'>192</a></td><td class="line-coverage quiet"><span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">const path = <span class="cstat-no" title="statement not covered" >require('path');</span>
const fs = <span class="cstat-no" title="statement not covered" >require('fs');</span>
const Store = <span class="cstat-no" title="statement not covered" >require('electron-store');</span>
&nbsp;
/**
 * Configuration manager for the fleet management system
 */
class ConfigManager {
<span class="fstat-no" title="function not covered" >  co</span>nstructor(options = <span class="branch-0 cbranch-no" title="branch not covered" >{})</span> {
<span class="cstat-no" title="statement not covered" >    this.store = new Store();</span>
<span class="cstat-no" title="statement not covered" >    this.envPath = path.join(process.cwd(), '.env');</span>
<span class="cstat-no" title="statement not covered" >    this.loadEnvFile(); </span>// Load .env first to make process.env available
&nbsp;
<span class="cstat-no" title="statement not covered" >    this.environment = options.environment || process.env.NODE_ENV || 'development';</span>
<span class="cstat-no" title="statement not covered" >    this.maxAgents = parseInt(process.env.MAX_AGENTS, 10) || options.maxAgents || 30;</span>
<span class="cstat-no" title="statement not covered" >    this.agentLaunchInterval = parseInt(process.env.AGENT_LAUNCH_INTERVAL, 10) || options.agentLaunchInterval || 5000;</span>
<span class="cstat-no" title="statement not covered" >    this.dashboardPort = parseInt(process.env.DASHBOARD_PORT, 10) || options.dashboardPort || 3000;</span>
<span class="cstat-no" title="statement not covered" >    this.databasePath = process.env.DATABASE_PATH || options.databasePath || './db/fleet-manager.db';</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    this.twitchClientId = process.env.TWITCH_CLIENT_ID || options.twitchClientId;</span>
<span class="cstat-no" title="statement not covered" >    this.twitchClientSecret = process.env.TWITCH_CLIENT_SECRET || options.twitchClientSecret;</span>
<span class="cstat-no" title="statement not covered" >    this.geminiApiKey = process.env.GEMINI_API_KEY || options.geminiApiKey;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    this.accountsPath = process.env.ACCOUNTS_PATH || options.accountsPath || './config/accounts.json';</span>
<span class="cstat-no" title="statement not covered" >    this.streamersPath = process.env.STREAMERS_PATH || options.streamersPath || './config/rpg_streamers.json';</span>
<span class="cstat-no" title="statement not covered" >    this.logPath = process.env.LOG_PATH || options.logPath || './logs';</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    this.messageMinInterval = parseInt(process.env.MESSAGE_MIN_INTERVAL, 10) || options.messageMinInterval || 300000;</span>
<span class="cstat-no" title="statement not covered" >    this.messageRandomization = parseFloat(process.env.MESSAGE_RANDOMIZATION) || options.messageRandomization || 0.5;</span>
<span class="cstat-no" title="statement not covered" >    this.categoryCheckInterval = parseInt(process.env.CATEGORY_CHECK_INTERVAL, 10) || options.categoryCheckInterval || 300000;</span>
<span class="cstat-no" title="statement not covered" >    this.maxRetries = parseInt(process.env.MAX_RETRIES, 10) || options.maxRetries || 3;</span>
<span class="cstat-no" title="statement not covered" >    this.retryBackoffBase = parseInt(process.env.RETRY_BACKOFF_BASE, 10) || options.retryBackoffBase || 5000;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    this.maxSameStreamChatPercentage = parseFloat(process.env.MAX_SAME_STREAM_CHAT_PERCENTAGE) || options.maxSameStreamChatPercentage || 0.3;</span>
<span class="cstat-no" title="statement not covered" >    this.maxMessagesPerHour = parseInt(process.env.MAX_MESSAGES_PER_HOUR, 10) || options.maxMessagesPerHour || 3;</span>
<span class="cstat-no" title="statement not covered" >    this.browserWidth = parseInt(process.env.BROWSER_WIDTH, 10) || options.browserWidth || 1280;</span>
<span class="cstat-no" title="statement not covered" >    this.browserHeight = parseInt(process.env.BROWSER_HEIGHT, 10) || options.browserHeight || 720;</span>
    
<span class="cstat-no" title="statement not covered" >    this.debug = (process.env.DEBUG === 'true') || options.debug || false;</span>
<span class="cstat-no" title="statement not covered" >    this.saveScreenshots = (process.env.SAVE_SCREENSHOTS === 'true') || options.saveScreenshots || false;</span>
&nbsp;
    // Distributed Deployment Settings
<span class="cstat-no" title="statement not covered" >    this.distributedEnabled = (process.env.DISTRIBUTED_ENABLED === 'true') || options.distributedEnabled || false;</span>
<span class="cstat-no" title="statement not covered" >    this.nodeRole = process.env.NODE_ROLE || options.nodeRole || 'master'; </span>// 'master' or 'worker'
<span class="cstat-no" title="statement not covered" >    this.masterNodeUrl = process.env.MASTER_NODE_URL || options.masterNodeUrl || 'http://localhost:12345';</span>
<span class="cstat-no" title="statement not covered" >    this.nodePort = parseInt(process.env.NODE_PORT, 10) || options.nodePort || 12345;</span>
<span class="cstat-no" title="statement not covered" >    this.nodeId = process.env.NODE_ID || options.nodeId || (this.nodeRole === 'master' ? 'master-node' : `worker-node-${Date.now()}`);</span>
&nbsp;
    // The existing scaleConfig seems fine, no need to tie it to .env for now unless specified
<span class="cstat-no" title="statement not covered" >    this.scaleConfig = {</span>
      small: { level: 'small', maxAgents: 30, cpuThreshold: 70, memoryThreshold: 80 },
      medium: { level: 'medium', maxAgents: 60, cpuThreshold: 75, memoryThreshold: 85 },
      large: { level: 'large', maxAgents: 90, cpuThreshold: 80, memoryThreshold: 90 },
      xlarge: { level: 'xlarge', maxAgents: 120, cpuThreshold: 85, memoryThreshold: 95 }
    };
&nbsp;
    // Load any overrides from electron-store, potentially overriding .env or defaults
<span class="cstat-no" title="statement not covered" >    this.loadFromStore();</span>
  }
&nbsp;
<span class="fstat-no" title="function not covered" >  lo</span>adEnvFile() {
<span class="cstat-no" title="statement not covered" >    try {</span>
<span class="cstat-no" title="statement not covered" >      if (fs.existsSync(this.envPath)) {</span>
        const envConfig = <span class="cstat-no" title="statement not covered" >require('dotenv').parse(fs.readFileSync(this.envPath));</span>
<span class="cstat-no" title="statement not covered" >        for (const key in envConfig) {</span>
<span class="cstat-no" title="statement not covered" >          if (process.env[key] === undefined) { // Only set if not already set by actual environment</span>
<span class="cstat-no" title="statement not covered" >             process.env[key] = envConfig[key];</span>
          }
        }
      }
    } catch (err) {
<span class="cstat-no" title="statement not covered" >      console.error('Error loading .env file:', err);</span>
    }
  }
&nbsp;
<span class="fstat-no" title="function not covered" >  lo</span>adFromStore() {
    const storedConfig = <span class="cstat-no" title="statement not covered" >this.store.store;</span>
<span class="cstat-no" title="statement not covered" >    for (const key in storedConfig) {</span>
        // Prioritize direct properties, then check if this key matches one of our config properties
<span class="cstat-no" title="statement not covered" >        if (this.hasOwnProperty(key)) {</span>
            const storedValue = <span class="cstat-no" title="statement not covered" >storedConfig[key];</span>
            // Basic type coercion based on expected types for new distributed configs
<span class="cstat-no" title="statement not covered" >            if (key === 'distributedEnabled' || key === 'saveScreenshots' || key === 'debug') {</span>
<span class="cstat-no" title="statement not covered" >                this[key] = (storedValue === 'true' || storedValue === true);</span>
            } else <span class="cstat-no" title="statement not covered" >if (key === 'nodePort' || key === 'maxAgents' /* add other int parsable here */ || key === 'agentLaunchInterval' || key === 'dashboardPort' || key === 'messageMinInterval' || key === 'categoryCheckInterval' || key === 'maxRetries' || key === 'retryBackoffBase' || key === 'maxMessagesPerHour' || key === 'browserWidth' || key === 'browserHeight') {</span>
<span class="cstat-no" title="statement not covered" >                this[key] = parseInt(storedValue, 10);</span>
            } else <span class="cstat-no" title="statement not covered" >if (key === 'messageRandomization' /* add other float parsable here */ || key === 'maxSameStreamChatPercentage') {</span>
<span class="cstat-no" title="statement not covered" >                this[key] = parseFloat(storedValue);</span>
            } else {
<span class="cstat-no" title="statement not covered" >                this[key] = storedValue;</span>
            }
        }
    }
  }
&nbsp;
  /**
   * Get configuration value
   * @param {string} key - Configuration key
   * @returns {*} Configuration value
   */
<span class="fstat-no" title="function not covered" >  ge</span>t(key) {
    // Prioritize instance properties (already loaded from .env, options, store)
<span class="cstat-no" title="statement not covered" >    if (this.hasOwnProperty(key)) {</span>
<span class="cstat-no" title="statement not covered" >        return this[key];</span>
    }
    // Fallback to process.env directly (e.g. for values not explicitly managed by constructor)
<span class="cstat-no" title="statement not covered" >    if (process.env.hasOwnProperty(key)) {</span>
<span class="cstat-no" title="statement not covered" >        return process.env[key];</span>
    }
    // Fallback to electron-store for keys not explicitly managed or unknown at construction time
<span class="cstat-no" title="statement not covered" >    return this.store.get(key);</span>
  }
&nbsp;
  /**
   * Set configuration value. Persists to electron-store.
   * @param {string} key - Configuration key
   * @param {*} value - Configuration value
   */
<span class="fstat-no" title="function not covered" >  se</span>t(key, value) {
<span class="cstat-no" title="statement not covered" >    this[key] = value; </span>// Update in-memory cache
<span class="cstat-no" title="statement not covered" >    this.store.set(key, value); </span>// Persist to electron-store
  }
&nbsp;
  /**
   * Get scale configuration for a specific level
   * @param {string} level - Scale level (small, medium, large, xlarge)
   * @returns {Object} Scale configuration
   */
<span class="fstat-no" title="function not covered" >  ge</span>tScaleConfig(level = <span class="branch-0 cbranch-no" title="branch not covered" >'small')</span> {
<span class="cstat-no" title="statement not covered" >    return this.scaleConfig[level];</span>
  }
&nbsp;
<span class="fstat-no" title="function not covered" >  ge</span>tRequired(key) {
    const value = <span class="cstat-no" title="statement not covered" >this.get(key);</span>
    // Check for null or undefined. Empty string might be a valid value for some configs.
<span class="cstat-no" title="statement not covered" >    if (value === null || value === undefined) {</span>
<span class="cstat-no" title="statement not covered" >      throw new Error(`Required configuration key "${key}" is not set`);</span>
    }
<span class="cstat-no" title="statement not covered" >    return value;</span>
  }
&nbsp;
  // Get all configuration as an object
<span class="fstat-no" title="function not covered" >  ge</span>tAll() {
    const config = <span class="cstat-no" title="statement not covered" >{};</span>
    // Collect all known config properties from the instance
<span class="cstat-no" title="statement not covered" >    Object.keys(this).forEach(<span class="fstat-no" title="function not covered" >ke</span>y =&gt; {</span>
<span class="cstat-no" title="statement not covered" >        if (key !== 'store' &amp;&amp; key !== 'envPath' &amp;&amp; key !== 'scaleConfig' &amp;&amp; typeof this[key] !== 'function') {</span>
<span class="cstat-no" title="statement not covered" >            config[key] = this[key];</span>
        }
    });
    // Include scaleConfig as a whole
<span class="cstat-no" title="statement not covered" >    config.scaleConfig = this.scaleConfig;</span>
    // Merge with anything else in process.env that might be relevant (e.g. system paths)
    // Be selective to avoid polluting with too much from process.env
<span class="cstat-no" title="statement not covered" >     for (const envKey in process.env) {</span>
<span class="cstat-no" title="statement not covered" >      if (!config.hasOwnProperty(envKey) &amp;&amp; (envKey.startsWith('TWITCH_') || envKey.startsWith('GEMINI_') || envKey.startsWith('NODE_'))) {</span>
<span class="cstat-no" title="statement not covered" >        config[envKey] = process.env[envKey];</span>
      }
    }
<span class="cstat-no" title="statement not covered" >    return config;</span>
  }
&nbsp;
  // Validate required configuration
<span class="fstat-no" title="function not covered" >  va</span>lidate() {
    const requiredKeys = <span class="cstat-no" title="statement not covered" >[</span>
      'twitchClientId',
      'twitchClientSecret',
      'geminiApiKey'
    ];
&nbsp;
    const missing = <span class="cstat-no" title="statement not covered" >requiredKeys.filter(<span class="fstat-no" title="function not covered" >ke</span>y =&gt; {</span>
        const value = <span class="cstat-no" title="statement not covered" >this.get(key);</span>
<span class="cstat-no" title="statement not covered" >        return value === null || value === undefined || value === ''; </span>// Also check for empty string for these critical keys
    });
&nbsp;
<span class="cstat-no" title="statement not covered" >    if (missing.length &gt; 0) {</span>
<span class="cstat-no" title="statement not covered" >      throw new Error(`Missing or empty required configuration: ${missing.join(', ')}`);</span>
    }
  }
&nbsp;
  // Clear all stored configuration in electron-store and reset in-memory
<span class="fstat-no" title="function not covered" >  cl</span>ear() {
<span class="cstat-no" title="statement not covered" >    this.store.clear();</span>
    // Re-initialize to defaults by creating a new temporary instance
    // and copying its properties, or re-run constructor logic carefully.
    // For simplicity, let's just clear the store. A full reset would re-run constructor.
    // Caller might need to re-instantiate ConfigManager for a full reset to defaults if desired.
<span class="cstat-no" title="statement not covered" >    console.log("Electron-store cleared. In-memory config might still hold values until next instantiation with no overrides.");</span>
  }
}
&nbsp;
<span class="cstat-no" title="statement not covered" >module.exports = ConfigManager; </span></pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-06-04T07:23:52.691Z
            </div>
        <script src="../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../sorter.js"></script>
        <script src="../block-navigation.js"></script>
    </body>
</html>
    