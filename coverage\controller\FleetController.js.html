
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for controller/FleetController.js</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../prettify.css" />
    <link rel="stylesheet" href="../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../index.html">All files</a> / <a href="index.html">controller</a> FleetController.js</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>0/331</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>0/161</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>0/39</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>0/324</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a>
<a name='L76'></a><a href='#L76'>76</a>
<a name='L77'></a><a href='#L77'>77</a>
<a name='L78'></a><a href='#L78'>78</a>
<a name='L79'></a><a href='#L79'>79</a>
<a name='L80'></a><a href='#L80'>80</a>
<a name='L81'></a><a href='#L81'>81</a>
<a name='L82'></a><a href='#L82'>82</a>
<a name='L83'></a><a href='#L83'>83</a>
<a name='L84'></a><a href='#L84'>84</a>
<a name='L85'></a><a href='#L85'>85</a>
<a name='L86'></a><a href='#L86'>86</a>
<a name='L87'></a><a href='#L87'>87</a>
<a name='L88'></a><a href='#L88'>88</a>
<a name='L89'></a><a href='#L89'>89</a>
<a name='L90'></a><a href='#L90'>90</a>
<a name='L91'></a><a href='#L91'>91</a>
<a name='L92'></a><a href='#L92'>92</a>
<a name='L93'></a><a href='#L93'>93</a>
<a name='L94'></a><a href='#L94'>94</a>
<a name='L95'></a><a href='#L95'>95</a>
<a name='L96'></a><a href='#L96'>96</a>
<a name='L97'></a><a href='#L97'>97</a>
<a name='L98'></a><a href='#L98'>98</a>
<a name='L99'></a><a href='#L99'>99</a>
<a name='L100'></a><a href='#L100'>100</a>
<a name='L101'></a><a href='#L101'>101</a>
<a name='L102'></a><a href='#L102'>102</a>
<a name='L103'></a><a href='#L103'>103</a>
<a name='L104'></a><a href='#L104'>104</a>
<a name='L105'></a><a href='#L105'>105</a>
<a name='L106'></a><a href='#L106'>106</a>
<a name='L107'></a><a href='#L107'>107</a>
<a name='L108'></a><a href='#L108'>108</a>
<a name='L109'></a><a href='#L109'>109</a>
<a name='L110'></a><a href='#L110'>110</a>
<a name='L111'></a><a href='#L111'>111</a>
<a name='L112'></a><a href='#L112'>112</a>
<a name='L113'></a><a href='#L113'>113</a>
<a name='L114'></a><a href='#L114'>114</a>
<a name='L115'></a><a href='#L115'>115</a>
<a name='L116'></a><a href='#L116'>116</a>
<a name='L117'></a><a href='#L117'>117</a>
<a name='L118'></a><a href='#L118'>118</a>
<a name='L119'></a><a href='#L119'>119</a>
<a name='L120'></a><a href='#L120'>120</a>
<a name='L121'></a><a href='#L121'>121</a>
<a name='L122'></a><a href='#L122'>122</a>
<a name='L123'></a><a href='#L123'>123</a>
<a name='L124'></a><a href='#L124'>124</a>
<a name='L125'></a><a href='#L125'>125</a>
<a name='L126'></a><a href='#L126'>126</a>
<a name='L127'></a><a href='#L127'>127</a>
<a name='L128'></a><a href='#L128'>128</a>
<a name='L129'></a><a href='#L129'>129</a>
<a name='L130'></a><a href='#L130'>130</a>
<a name='L131'></a><a href='#L131'>131</a>
<a name='L132'></a><a href='#L132'>132</a>
<a name='L133'></a><a href='#L133'>133</a>
<a name='L134'></a><a href='#L134'>134</a>
<a name='L135'></a><a href='#L135'>135</a>
<a name='L136'></a><a href='#L136'>136</a>
<a name='L137'></a><a href='#L137'>137</a>
<a name='L138'></a><a href='#L138'>138</a>
<a name='L139'></a><a href='#L139'>139</a>
<a name='L140'></a><a href='#L140'>140</a>
<a name='L141'></a><a href='#L141'>141</a>
<a name='L142'></a><a href='#L142'>142</a>
<a name='L143'></a><a href='#L143'>143</a>
<a name='L144'></a><a href='#L144'>144</a>
<a name='L145'></a><a href='#L145'>145</a>
<a name='L146'></a><a href='#L146'>146</a>
<a name='L147'></a><a href='#L147'>147</a>
<a name='L148'></a><a href='#L148'>148</a>
<a name='L149'></a><a href='#L149'>149</a>
<a name='L150'></a><a href='#L150'>150</a>
<a name='L151'></a><a href='#L151'>151</a>
<a name='L152'></a><a href='#L152'>152</a>
<a name='L153'></a><a href='#L153'>153</a>
<a name='L154'></a><a href='#L154'>154</a>
<a name='L155'></a><a href='#L155'>155</a>
<a name='L156'></a><a href='#L156'>156</a>
<a name='L157'></a><a href='#L157'>157</a>
<a name='L158'></a><a href='#L158'>158</a>
<a name='L159'></a><a href='#L159'>159</a>
<a name='L160'></a><a href='#L160'>160</a>
<a name='L161'></a><a href='#L161'>161</a>
<a name='L162'></a><a href='#L162'>162</a>
<a name='L163'></a><a href='#L163'>163</a>
<a name='L164'></a><a href='#L164'>164</a>
<a name='L165'></a><a href='#L165'>165</a>
<a name='L166'></a><a href='#L166'>166</a>
<a name='L167'></a><a href='#L167'>167</a>
<a name='L168'></a><a href='#L168'>168</a>
<a name='L169'></a><a href='#L169'>169</a>
<a name='L170'></a><a href='#L170'>170</a>
<a name='L171'></a><a href='#L171'>171</a>
<a name='L172'></a><a href='#L172'>172</a>
<a name='L173'></a><a href='#L173'>173</a>
<a name='L174'></a><a href='#L174'>174</a>
<a name='L175'></a><a href='#L175'>175</a>
<a name='L176'></a><a href='#L176'>176</a>
<a name='L177'></a><a href='#L177'>177</a>
<a name='L178'></a><a href='#L178'>178</a>
<a name='L179'></a><a href='#L179'>179</a>
<a name='L180'></a><a href='#L180'>180</a>
<a name='L181'></a><a href='#L181'>181</a>
<a name='L182'></a><a href='#L182'>182</a>
<a name='L183'></a><a href='#L183'>183</a>
<a name='L184'></a><a href='#L184'>184</a>
<a name='L185'></a><a href='#L185'>185</a>
<a name='L186'></a><a href='#L186'>186</a>
<a name='L187'></a><a href='#L187'>187</a>
<a name='L188'></a><a href='#L188'>188</a>
<a name='L189'></a><a href='#L189'>189</a>
<a name='L190'></a><a href='#L190'>190</a>
<a name='L191'></a><a href='#L191'>191</a>
<a name='L192'></a><a href='#L192'>192</a>
<a name='L193'></a><a href='#L193'>193</a>
<a name='L194'></a><a href='#L194'>194</a>
<a name='L195'></a><a href='#L195'>195</a>
<a name='L196'></a><a href='#L196'>196</a>
<a name='L197'></a><a href='#L197'>197</a>
<a name='L198'></a><a href='#L198'>198</a>
<a name='L199'></a><a href='#L199'>199</a>
<a name='L200'></a><a href='#L200'>200</a>
<a name='L201'></a><a href='#L201'>201</a>
<a name='L202'></a><a href='#L202'>202</a>
<a name='L203'></a><a href='#L203'>203</a>
<a name='L204'></a><a href='#L204'>204</a>
<a name='L205'></a><a href='#L205'>205</a>
<a name='L206'></a><a href='#L206'>206</a>
<a name='L207'></a><a href='#L207'>207</a>
<a name='L208'></a><a href='#L208'>208</a>
<a name='L209'></a><a href='#L209'>209</a>
<a name='L210'></a><a href='#L210'>210</a>
<a name='L211'></a><a href='#L211'>211</a>
<a name='L212'></a><a href='#L212'>212</a>
<a name='L213'></a><a href='#L213'>213</a>
<a name='L214'></a><a href='#L214'>214</a>
<a name='L215'></a><a href='#L215'>215</a>
<a name='L216'></a><a href='#L216'>216</a>
<a name='L217'></a><a href='#L217'>217</a>
<a name='L218'></a><a href='#L218'>218</a>
<a name='L219'></a><a href='#L219'>219</a>
<a name='L220'></a><a href='#L220'>220</a>
<a name='L221'></a><a href='#L221'>221</a>
<a name='L222'></a><a href='#L222'>222</a>
<a name='L223'></a><a href='#L223'>223</a>
<a name='L224'></a><a href='#L224'>224</a>
<a name='L225'></a><a href='#L225'>225</a>
<a name='L226'></a><a href='#L226'>226</a>
<a name='L227'></a><a href='#L227'>227</a>
<a name='L228'></a><a href='#L228'>228</a>
<a name='L229'></a><a href='#L229'>229</a>
<a name='L230'></a><a href='#L230'>230</a>
<a name='L231'></a><a href='#L231'>231</a>
<a name='L232'></a><a href='#L232'>232</a>
<a name='L233'></a><a href='#L233'>233</a>
<a name='L234'></a><a href='#L234'>234</a>
<a name='L235'></a><a href='#L235'>235</a>
<a name='L236'></a><a href='#L236'>236</a>
<a name='L237'></a><a href='#L237'>237</a>
<a name='L238'></a><a href='#L238'>238</a>
<a name='L239'></a><a href='#L239'>239</a>
<a name='L240'></a><a href='#L240'>240</a>
<a name='L241'></a><a href='#L241'>241</a>
<a name='L242'></a><a href='#L242'>242</a>
<a name='L243'></a><a href='#L243'>243</a>
<a name='L244'></a><a href='#L244'>244</a>
<a name='L245'></a><a href='#L245'>245</a>
<a name='L246'></a><a href='#L246'>246</a>
<a name='L247'></a><a href='#L247'>247</a>
<a name='L248'></a><a href='#L248'>248</a>
<a name='L249'></a><a href='#L249'>249</a>
<a name='L250'></a><a href='#L250'>250</a>
<a name='L251'></a><a href='#L251'>251</a>
<a name='L252'></a><a href='#L252'>252</a>
<a name='L253'></a><a href='#L253'>253</a>
<a name='L254'></a><a href='#L254'>254</a>
<a name='L255'></a><a href='#L255'>255</a>
<a name='L256'></a><a href='#L256'>256</a>
<a name='L257'></a><a href='#L257'>257</a>
<a name='L258'></a><a href='#L258'>258</a>
<a name='L259'></a><a href='#L259'>259</a>
<a name='L260'></a><a href='#L260'>260</a>
<a name='L261'></a><a href='#L261'>261</a>
<a name='L262'></a><a href='#L262'>262</a>
<a name='L263'></a><a href='#L263'>263</a>
<a name='L264'></a><a href='#L264'>264</a>
<a name='L265'></a><a href='#L265'>265</a>
<a name='L266'></a><a href='#L266'>266</a>
<a name='L267'></a><a href='#L267'>267</a>
<a name='L268'></a><a href='#L268'>268</a>
<a name='L269'></a><a href='#L269'>269</a>
<a name='L270'></a><a href='#L270'>270</a>
<a name='L271'></a><a href='#L271'>271</a>
<a name='L272'></a><a href='#L272'>272</a>
<a name='L273'></a><a href='#L273'>273</a>
<a name='L274'></a><a href='#L274'>274</a>
<a name='L275'></a><a href='#L275'>275</a>
<a name='L276'></a><a href='#L276'>276</a>
<a name='L277'></a><a href='#L277'>277</a>
<a name='L278'></a><a href='#L278'>278</a>
<a name='L279'></a><a href='#L279'>279</a>
<a name='L280'></a><a href='#L280'>280</a>
<a name='L281'></a><a href='#L281'>281</a>
<a name='L282'></a><a href='#L282'>282</a>
<a name='L283'></a><a href='#L283'>283</a>
<a name='L284'></a><a href='#L284'>284</a>
<a name='L285'></a><a href='#L285'>285</a>
<a name='L286'></a><a href='#L286'>286</a>
<a name='L287'></a><a href='#L287'>287</a>
<a name='L288'></a><a href='#L288'>288</a>
<a name='L289'></a><a href='#L289'>289</a>
<a name='L290'></a><a href='#L290'>290</a>
<a name='L291'></a><a href='#L291'>291</a>
<a name='L292'></a><a href='#L292'>292</a>
<a name='L293'></a><a href='#L293'>293</a>
<a name='L294'></a><a href='#L294'>294</a>
<a name='L295'></a><a href='#L295'>295</a>
<a name='L296'></a><a href='#L296'>296</a>
<a name='L297'></a><a href='#L297'>297</a>
<a name='L298'></a><a href='#L298'>298</a>
<a name='L299'></a><a href='#L299'>299</a>
<a name='L300'></a><a href='#L300'>300</a>
<a name='L301'></a><a href='#L301'>301</a>
<a name='L302'></a><a href='#L302'>302</a>
<a name='L303'></a><a href='#L303'>303</a>
<a name='L304'></a><a href='#L304'>304</a>
<a name='L305'></a><a href='#L305'>305</a>
<a name='L306'></a><a href='#L306'>306</a>
<a name='L307'></a><a href='#L307'>307</a>
<a name='L308'></a><a href='#L308'>308</a>
<a name='L309'></a><a href='#L309'>309</a>
<a name='L310'></a><a href='#L310'>310</a>
<a name='L311'></a><a href='#L311'>311</a>
<a name='L312'></a><a href='#L312'>312</a>
<a name='L313'></a><a href='#L313'>313</a>
<a name='L314'></a><a href='#L314'>314</a>
<a name='L315'></a><a href='#L315'>315</a>
<a name='L316'></a><a href='#L316'>316</a>
<a name='L317'></a><a href='#L317'>317</a>
<a name='L318'></a><a href='#L318'>318</a>
<a name='L319'></a><a href='#L319'>319</a>
<a name='L320'></a><a href='#L320'>320</a>
<a name='L321'></a><a href='#L321'>321</a>
<a name='L322'></a><a href='#L322'>322</a>
<a name='L323'></a><a href='#L323'>323</a>
<a name='L324'></a><a href='#L324'>324</a>
<a name='L325'></a><a href='#L325'>325</a>
<a name='L326'></a><a href='#L326'>326</a>
<a name='L327'></a><a href='#L327'>327</a>
<a name='L328'></a><a href='#L328'>328</a>
<a name='L329'></a><a href='#L329'>329</a>
<a name='L330'></a><a href='#L330'>330</a>
<a name='L331'></a><a href='#L331'>331</a>
<a name='L332'></a><a href='#L332'>332</a>
<a name='L333'></a><a href='#L333'>333</a>
<a name='L334'></a><a href='#L334'>334</a>
<a name='L335'></a><a href='#L335'>335</a>
<a name='L336'></a><a href='#L336'>336</a>
<a name='L337'></a><a href='#L337'>337</a>
<a name='L338'></a><a href='#L338'>338</a>
<a name='L339'></a><a href='#L339'>339</a>
<a name='L340'></a><a href='#L340'>340</a>
<a name='L341'></a><a href='#L341'>341</a>
<a name='L342'></a><a href='#L342'>342</a>
<a name='L343'></a><a href='#L343'>343</a>
<a name='L344'></a><a href='#L344'>344</a>
<a name='L345'></a><a href='#L345'>345</a>
<a name='L346'></a><a href='#L346'>346</a>
<a name='L347'></a><a href='#L347'>347</a>
<a name='L348'></a><a href='#L348'>348</a>
<a name='L349'></a><a href='#L349'>349</a>
<a name='L350'></a><a href='#L350'>350</a>
<a name='L351'></a><a href='#L351'>351</a>
<a name='L352'></a><a href='#L352'>352</a>
<a name='L353'></a><a href='#L353'>353</a>
<a name='L354'></a><a href='#L354'>354</a>
<a name='L355'></a><a href='#L355'>355</a>
<a name='L356'></a><a href='#L356'>356</a>
<a name='L357'></a><a href='#L357'>357</a>
<a name='L358'></a><a href='#L358'>358</a>
<a name='L359'></a><a href='#L359'>359</a>
<a name='L360'></a><a href='#L360'>360</a>
<a name='L361'></a><a href='#L361'>361</a>
<a name='L362'></a><a href='#L362'>362</a>
<a name='L363'></a><a href='#L363'>363</a>
<a name='L364'></a><a href='#L364'>364</a>
<a name='L365'></a><a href='#L365'>365</a>
<a name='L366'></a><a href='#L366'>366</a>
<a name='L367'></a><a href='#L367'>367</a>
<a name='L368'></a><a href='#L368'>368</a>
<a name='L369'></a><a href='#L369'>369</a>
<a name='L370'></a><a href='#L370'>370</a>
<a name='L371'></a><a href='#L371'>371</a>
<a name='L372'></a><a href='#L372'>372</a>
<a name='L373'></a><a href='#L373'>373</a>
<a name='L374'></a><a href='#L374'>374</a>
<a name='L375'></a><a href='#L375'>375</a>
<a name='L376'></a><a href='#L376'>376</a>
<a name='L377'></a><a href='#L377'>377</a>
<a name='L378'></a><a href='#L378'>378</a>
<a name='L379'></a><a href='#L379'>379</a>
<a name='L380'></a><a href='#L380'>380</a>
<a name='L381'></a><a href='#L381'>381</a>
<a name='L382'></a><a href='#L382'>382</a>
<a name='L383'></a><a href='#L383'>383</a>
<a name='L384'></a><a href='#L384'>384</a>
<a name='L385'></a><a href='#L385'>385</a>
<a name='L386'></a><a href='#L386'>386</a>
<a name='L387'></a><a href='#L387'>387</a>
<a name='L388'></a><a href='#L388'>388</a>
<a name='L389'></a><a href='#L389'>389</a>
<a name='L390'></a><a href='#L390'>390</a>
<a name='L391'></a><a href='#L391'>391</a>
<a name='L392'></a><a href='#L392'>392</a>
<a name='L393'></a><a href='#L393'>393</a>
<a name='L394'></a><a href='#L394'>394</a>
<a name='L395'></a><a href='#L395'>395</a>
<a name='L396'></a><a href='#L396'>396</a>
<a name='L397'></a><a href='#L397'>397</a>
<a name='L398'></a><a href='#L398'>398</a>
<a name='L399'></a><a href='#L399'>399</a>
<a name='L400'></a><a href='#L400'>400</a>
<a name='L401'></a><a href='#L401'>401</a>
<a name='L402'></a><a href='#L402'>402</a>
<a name='L403'></a><a href='#L403'>403</a>
<a name='L404'></a><a href='#L404'>404</a>
<a name='L405'></a><a href='#L405'>405</a>
<a name='L406'></a><a href='#L406'>406</a>
<a name='L407'></a><a href='#L407'>407</a>
<a name='L408'></a><a href='#L408'>408</a>
<a name='L409'></a><a href='#L409'>409</a>
<a name='L410'></a><a href='#L410'>410</a>
<a name='L411'></a><a href='#L411'>411</a>
<a name='L412'></a><a href='#L412'>412</a>
<a name='L413'></a><a href='#L413'>413</a>
<a name='L414'></a><a href='#L414'>414</a>
<a name='L415'></a><a href='#L415'>415</a>
<a name='L416'></a><a href='#L416'>416</a>
<a name='L417'></a><a href='#L417'>417</a>
<a name='L418'></a><a href='#L418'>418</a>
<a name='L419'></a><a href='#L419'>419</a>
<a name='L420'></a><a href='#L420'>420</a>
<a name='L421'></a><a href='#L421'>421</a>
<a name='L422'></a><a href='#L422'>422</a>
<a name='L423'></a><a href='#L423'>423</a>
<a name='L424'></a><a href='#L424'>424</a>
<a name='L425'></a><a href='#L425'>425</a>
<a name='L426'></a><a href='#L426'>426</a>
<a name='L427'></a><a href='#L427'>427</a>
<a name='L428'></a><a href='#L428'>428</a>
<a name='L429'></a><a href='#L429'>429</a>
<a name='L430'></a><a href='#L430'>430</a>
<a name='L431'></a><a href='#L431'>431</a>
<a name='L432'></a><a href='#L432'>432</a>
<a name='L433'></a><a href='#L433'>433</a>
<a name='L434'></a><a href='#L434'>434</a>
<a name='L435'></a><a href='#L435'>435</a>
<a name='L436'></a><a href='#L436'>436</a>
<a name='L437'></a><a href='#L437'>437</a>
<a name='L438'></a><a href='#L438'>438</a>
<a name='L439'></a><a href='#L439'>439</a>
<a name='L440'></a><a href='#L440'>440</a>
<a name='L441'></a><a href='#L441'>441</a>
<a name='L442'></a><a href='#L442'>442</a>
<a name='L443'></a><a href='#L443'>443</a>
<a name='L444'></a><a href='#L444'>444</a>
<a name='L445'></a><a href='#L445'>445</a>
<a name='L446'></a><a href='#L446'>446</a>
<a name='L447'></a><a href='#L447'>447</a>
<a name='L448'></a><a href='#L448'>448</a>
<a name='L449'></a><a href='#L449'>449</a>
<a name='L450'></a><a href='#L450'>450</a>
<a name='L451'></a><a href='#L451'>451</a>
<a name='L452'></a><a href='#L452'>452</a>
<a name='L453'></a><a href='#L453'>453</a>
<a name='L454'></a><a href='#L454'>454</a>
<a name='L455'></a><a href='#L455'>455</a>
<a name='L456'></a><a href='#L456'>456</a>
<a name='L457'></a><a href='#L457'>457</a>
<a name='L458'></a><a href='#L458'>458</a>
<a name='L459'></a><a href='#L459'>459</a>
<a name='L460'></a><a href='#L460'>460</a>
<a name='L461'></a><a href='#L461'>461</a>
<a name='L462'></a><a href='#L462'>462</a>
<a name='L463'></a><a href='#L463'>463</a>
<a name='L464'></a><a href='#L464'>464</a>
<a name='L465'></a><a href='#L465'>465</a>
<a name='L466'></a><a href='#L466'>466</a>
<a name='L467'></a><a href='#L467'>467</a>
<a name='L468'></a><a href='#L468'>468</a>
<a name='L469'></a><a href='#L469'>469</a>
<a name='L470'></a><a href='#L470'>470</a>
<a name='L471'></a><a href='#L471'>471</a>
<a name='L472'></a><a href='#L472'>472</a>
<a name='L473'></a><a href='#L473'>473</a>
<a name='L474'></a><a href='#L474'>474</a>
<a name='L475'></a><a href='#L475'>475</a>
<a name='L476'></a><a href='#L476'>476</a>
<a name='L477'></a><a href='#L477'>477</a>
<a name='L478'></a><a href='#L478'>478</a>
<a name='L479'></a><a href='#L479'>479</a>
<a name='L480'></a><a href='#L480'>480</a>
<a name='L481'></a><a href='#L481'>481</a>
<a name='L482'></a><a href='#L482'>482</a>
<a name='L483'></a><a href='#L483'>483</a>
<a name='L484'></a><a href='#L484'>484</a>
<a name='L485'></a><a href='#L485'>485</a>
<a name='L486'></a><a href='#L486'>486</a>
<a name='L487'></a><a href='#L487'>487</a>
<a name='L488'></a><a href='#L488'>488</a>
<a name='L489'></a><a href='#L489'>489</a>
<a name='L490'></a><a href='#L490'>490</a>
<a name='L491'></a><a href='#L491'>491</a>
<a name='L492'></a><a href='#L492'>492</a>
<a name='L493'></a><a href='#L493'>493</a>
<a name='L494'></a><a href='#L494'>494</a>
<a name='L495'></a><a href='#L495'>495</a>
<a name='L496'></a><a href='#L496'>496</a>
<a name='L497'></a><a href='#L497'>497</a>
<a name='L498'></a><a href='#L498'>498</a>
<a name='L499'></a><a href='#L499'>499</a>
<a name='L500'></a><a href='#L500'>500</a>
<a name='L501'></a><a href='#L501'>501</a>
<a name='L502'></a><a href='#L502'>502</a>
<a name='L503'></a><a href='#L503'>503</a>
<a name='L504'></a><a href='#L504'>504</a>
<a name='L505'></a><a href='#L505'>505</a>
<a name='L506'></a><a href='#L506'>506</a>
<a name='L507'></a><a href='#L507'>507</a>
<a name='L508'></a><a href='#L508'>508</a>
<a name='L509'></a><a href='#L509'>509</a>
<a name='L510'></a><a href='#L510'>510</a>
<a name='L511'></a><a href='#L511'>511</a>
<a name='L512'></a><a href='#L512'>512</a>
<a name='L513'></a><a href='#L513'>513</a>
<a name='L514'></a><a href='#L514'>514</a>
<a name='L515'></a><a href='#L515'>515</a>
<a name='L516'></a><a href='#L516'>516</a>
<a name='L517'></a><a href='#L517'>517</a>
<a name='L518'></a><a href='#L518'>518</a>
<a name='L519'></a><a href='#L519'>519</a>
<a name='L520'></a><a href='#L520'>520</a>
<a name='L521'></a><a href='#L521'>521</a>
<a name='L522'></a><a href='#L522'>522</a>
<a name='L523'></a><a href='#L523'>523</a>
<a name='L524'></a><a href='#L524'>524</a>
<a name='L525'></a><a href='#L525'>525</a>
<a name='L526'></a><a href='#L526'>526</a>
<a name='L527'></a><a href='#L527'>527</a>
<a name='L528'></a><a href='#L528'>528</a>
<a name='L529'></a><a href='#L529'>529</a>
<a name='L530'></a><a href='#L530'>530</a>
<a name='L531'></a><a href='#L531'>531</a>
<a name='L532'></a><a href='#L532'>532</a>
<a name='L533'></a><a href='#L533'>533</a>
<a name='L534'></a><a href='#L534'>534</a>
<a name='L535'></a><a href='#L535'>535</a>
<a name='L536'></a><a href='#L536'>536</a>
<a name='L537'></a><a href='#L537'>537</a>
<a name='L538'></a><a href='#L538'>538</a>
<a name='L539'></a><a href='#L539'>539</a>
<a name='L540'></a><a href='#L540'>540</a>
<a name='L541'></a><a href='#L541'>541</a>
<a name='L542'></a><a href='#L542'>542</a>
<a name='L543'></a><a href='#L543'>543</a>
<a name='L544'></a><a href='#L544'>544</a>
<a name='L545'></a><a href='#L545'>545</a>
<a name='L546'></a><a href='#L546'>546</a>
<a name='L547'></a><a href='#L547'>547</a>
<a name='L548'></a><a href='#L548'>548</a>
<a name='L549'></a><a href='#L549'>549</a>
<a name='L550'></a><a href='#L550'>550</a>
<a name='L551'></a><a href='#L551'>551</a>
<a name='L552'></a><a href='#L552'>552</a>
<a name='L553'></a><a href='#L553'>553</a>
<a name='L554'></a><a href='#L554'>554</a>
<a name='L555'></a><a href='#L555'>555</a>
<a name='L556'></a><a href='#L556'>556</a>
<a name='L557'></a><a href='#L557'>557</a>
<a name='L558'></a><a href='#L558'>558</a>
<a name='L559'></a><a href='#L559'>559</a>
<a name='L560'></a><a href='#L560'>560</a>
<a name='L561'></a><a href='#L561'>561</a>
<a name='L562'></a><a href='#L562'>562</a>
<a name='L563'></a><a href='#L563'>563</a>
<a name='L564'></a><a href='#L564'>564</a>
<a name='L565'></a><a href='#L565'>565</a>
<a name='L566'></a><a href='#L566'>566</a>
<a name='L567'></a><a href='#L567'>567</a>
<a name='L568'></a><a href='#L568'>568</a>
<a name='L569'></a><a href='#L569'>569</a>
<a name='L570'></a><a href='#L570'>570</a>
<a name='L571'></a><a href='#L571'>571</a>
<a name='L572'></a><a href='#L572'>572</a>
<a name='L573'></a><a href='#L573'>573</a>
<a name='L574'></a><a href='#L574'>574</a>
<a name='L575'></a><a href='#L575'>575</a>
<a name='L576'></a><a href='#L576'>576</a>
<a name='L577'></a><a href='#L577'>577</a>
<a name='L578'></a><a href='#L578'>578</a>
<a name='L579'></a><a href='#L579'>579</a>
<a name='L580'></a><a href='#L580'>580</a>
<a name='L581'></a><a href='#L581'>581</a>
<a name='L582'></a><a href='#L582'>582</a>
<a name='L583'></a><a href='#L583'>583</a>
<a name='L584'></a><a href='#L584'>584</a>
<a name='L585'></a><a href='#L585'>585</a>
<a name='L586'></a><a href='#L586'>586</a>
<a name='L587'></a><a href='#L587'>587</a>
<a name='L588'></a><a href='#L588'>588</a>
<a name='L589'></a><a href='#L589'>589</a>
<a name='L590'></a><a href='#L590'>590</a>
<a name='L591'></a><a href='#L591'>591</a>
<a name='L592'></a><a href='#L592'>592</a>
<a name='L593'></a><a href='#L593'>593</a>
<a name='L594'></a><a href='#L594'>594</a>
<a name='L595'></a><a href='#L595'>595</a>
<a name='L596'></a><a href='#L596'>596</a>
<a name='L597'></a><a href='#L597'>597</a>
<a name='L598'></a><a href='#L598'>598</a>
<a name='L599'></a><a href='#L599'>599</a>
<a name='L600'></a><a href='#L600'>600</a>
<a name='L601'></a><a href='#L601'>601</a>
<a name='L602'></a><a href='#L602'>602</a>
<a name='L603'></a><a href='#L603'>603</a>
<a name='L604'></a><a href='#L604'>604</a>
<a name='L605'></a><a href='#L605'>605</a>
<a name='L606'></a><a href='#L606'>606</a>
<a name='L607'></a><a href='#L607'>607</a>
<a name='L608'></a><a href='#L608'>608</a>
<a name='L609'></a><a href='#L609'>609</a>
<a name='L610'></a><a href='#L610'>610</a>
<a name='L611'></a><a href='#L611'>611</a>
<a name='L612'></a><a href='#L612'>612</a>
<a name='L613'></a><a href='#L613'>613</a>
<a name='L614'></a><a href='#L614'>614</a>
<a name='L615'></a><a href='#L615'>615</a>
<a name='L616'></a><a href='#L616'>616</a>
<a name='L617'></a><a href='#L617'>617</a>
<a name='L618'></a><a href='#L618'>618</a>
<a name='L619'></a><a href='#L619'>619</a>
<a name='L620'></a><a href='#L620'>620</a>
<a name='L621'></a><a href='#L621'>621</a>
<a name='L622'></a><a href='#L622'>622</a>
<a name='L623'></a><a href='#L623'>623</a>
<a name='L624'></a><a href='#L624'>624</a>
<a name='L625'></a><a href='#L625'>625</a>
<a name='L626'></a><a href='#L626'>626</a>
<a name='L627'></a><a href='#L627'>627</a>
<a name='L628'></a><a href='#L628'>628</a>
<a name='L629'></a><a href='#L629'>629</a>
<a name='L630'></a><a href='#L630'>630</a>
<a name='L631'></a><a href='#L631'>631</a>
<a name='L632'></a><a href='#L632'>632</a>
<a name='L633'></a><a href='#L633'>633</a>
<a name='L634'></a><a href='#L634'>634</a>
<a name='L635'></a><a href='#L635'>635</a>
<a name='L636'></a><a href='#L636'>636</a>
<a name='L637'></a><a href='#L637'>637</a>
<a name='L638'></a><a href='#L638'>638</a>
<a name='L639'></a><a href='#L639'>639</a>
<a name='L640'></a><a href='#L640'>640</a>
<a name='L641'></a><a href='#L641'>641</a>
<a name='L642'></a><a href='#L642'>642</a>
<a name='L643'></a><a href='#L643'>643</a>
<a name='L644'></a><a href='#L644'>644</a>
<a name='L645'></a><a href='#L645'>645</a>
<a name='L646'></a><a href='#L646'>646</a>
<a name='L647'></a><a href='#L647'>647</a>
<a name='L648'></a><a href='#L648'>648</a>
<a name='L649'></a><a href='#L649'>649</a>
<a name='L650'></a><a href='#L650'>650</a>
<a name='L651'></a><a href='#L651'>651</a>
<a name='L652'></a><a href='#L652'>652</a>
<a name='L653'></a><a href='#L653'>653</a>
<a name='L654'></a><a href='#L654'>654</a>
<a name='L655'></a><a href='#L655'>655</a>
<a name='L656'></a><a href='#L656'>656</a>
<a name='L657'></a><a href='#L657'>657</a>
<a name='L658'></a><a href='#L658'>658</a>
<a name='L659'></a><a href='#L659'>659</a>
<a name='L660'></a><a href='#L660'>660</a>
<a name='L661'></a><a href='#L661'>661</a>
<a name='L662'></a><a href='#L662'>662</a>
<a name='L663'></a><a href='#L663'>663</a>
<a name='L664'></a><a href='#L664'>664</a>
<a name='L665'></a><a href='#L665'>665</a>
<a name='L666'></a><a href='#L666'>666</a>
<a name='L667'></a><a href='#L667'>667</a>
<a name='L668'></a><a href='#L668'>668</a>
<a name='L669'></a><a href='#L669'>669</a>
<a name='L670'></a><a href='#L670'>670</a>
<a name='L671'></a><a href='#L671'>671</a>
<a name='L672'></a><a href='#L672'>672</a>
<a name='L673'></a><a href='#L673'>673</a>
<a name='L674'></a><a href='#L674'>674</a>
<a name='L675'></a><a href='#L675'>675</a>
<a name='L676'></a><a href='#L676'>676</a>
<a name='L677'></a><a href='#L677'>677</a>
<a name='L678'></a><a href='#L678'>678</a>
<a name='L679'></a><a href='#L679'>679</a>
<a name='L680'></a><a href='#L680'>680</a>
<a name='L681'></a><a href='#L681'>681</a>
<a name='L682'></a><a href='#L682'>682</a>
<a name='L683'></a><a href='#L683'>683</a>
<a name='L684'></a><a href='#L684'>684</a>
<a name='L685'></a><a href='#L685'>685</a>
<a name='L686'></a><a href='#L686'>686</a>
<a name='L687'></a><a href='#L687'>687</a>
<a name='L688'></a><a href='#L688'>688</a>
<a name='L689'></a><a href='#L689'>689</a>
<a name='L690'></a><a href='#L690'>690</a>
<a name='L691'></a><a href='#L691'>691</a>
<a name='L692'></a><a href='#L692'>692</a>
<a name='L693'></a><a href='#L693'>693</a>
<a name='L694'></a><a href='#L694'>694</a>
<a name='L695'></a><a href='#L695'>695</a>
<a name='L696'></a><a href='#L696'>696</a>
<a name='L697'></a><a href='#L697'>697</a>
<a name='L698'></a><a href='#L698'>698</a>
<a name='L699'></a><a href='#L699'>699</a>
<a name='L700'></a><a href='#L700'>700</a>
<a name='L701'></a><a href='#L701'>701</a>
<a name='L702'></a><a href='#L702'>702</a>
<a name='L703'></a><a href='#L703'>703</a>
<a name='L704'></a><a href='#L704'>704</a>
<a name='L705'></a><a href='#L705'>705</a>
<a name='L706'></a><a href='#L706'>706</a>
<a name='L707'></a><a href='#L707'>707</a>
<a name='L708'></a><a href='#L708'>708</a>
<a name='L709'></a><a href='#L709'>709</a>
<a name='L710'></a><a href='#L710'>710</a>
<a name='L711'></a><a href='#L711'>711</a>
<a name='L712'></a><a href='#L712'>712</a>
<a name='L713'></a><a href='#L713'>713</a>
<a name='L714'></a><a href='#L714'>714</a>
<a name='L715'></a><a href='#L715'>715</a>
<a name='L716'></a><a href='#L716'>716</a>
<a name='L717'></a><a href='#L717'>717</a>
<a name='L718'></a><a href='#L718'>718</a>
<a name='L719'></a><a href='#L719'>719</a>
<a name='L720'></a><a href='#L720'>720</a>
<a name='L721'></a><a href='#L721'>721</a>
<a name='L722'></a><a href='#L722'>722</a>
<a name='L723'></a><a href='#L723'>723</a>
<a name='L724'></a><a href='#L724'>724</a>
<a name='L725'></a><a href='#L725'>725</a>
<a name='L726'></a><a href='#L726'>726</a>
<a name='L727'></a><a href='#L727'>727</a>
<a name='L728'></a><a href='#L728'>728</a>
<a name='L729'></a><a href='#L729'>729</a>
<a name='L730'></a><a href='#L730'>730</a>
<a name='L731'></a><a href='#L731'>731</a>
<a name='L732'></a><a href='#L732'>732</a>
<a name='L733'></a><a href='#L733'>733</a>
<a name='L734'></a><a href='#L734'>734</a>
<a name='L735'></a><a href='#L735'>735</a>
<a name='L736'></a><a href='#L736'>736</a>
<a name='L737'></a><a href='#L737'>737</a>
<a name='L738'></a><a href='#L738'>738</a>
<a name='L739'></a><a href='#L739'>739</a>
<a name='L740'></a><a href='#L740'>740</a>
<a name='L741'></a><a href='#L741'>741</a>
<a name='L742'></a><a href='#L742'>742</a>
<a name='L743'></a><a href='#L743'>743</a>
<a name='L744'></a><a href='#L744'>744</a>
<a name='L745'></a><a href='#L745'>745</a>
<a name='L746'></a><a href='#L746'>746</a>
<a name='L747'></a><a href='#L747'>747</a>
<a name='L748'></a><a href='#L748'>748</a>
<a name='L749'></a><a href='#L749'>749</a>
<a name='L750'></a><a href='#L750'>750</a>
<a name='L751'></a><a href='#L751'>751</a>
<a name='L752'></a><a href='#L752'>752</a>
<a name='L753'></a><a href='#L753'>753</a>
<a name='L754'></a><a href='#L754'>754</a>
<a name='L755'></a><a href='#L755'>755</a>
<a name='L756'></a><a href='#L756'>756</a>
<a name='L757'></a><a href='#L757'>757</a>
<a name='L758'></a><a href='#L758'>758</a>
<a name='L759'></a><a href='#L759'>759</a>
<a name='L760'></a><a href='#L760'>760</a>
<a name='L761'></a><a href='#L761'>761</a>
<a name='L762'></a><a href='#L762'>762</a>
<a name='L763'></a><a href='#L763'>763</a>
<a name='L764'></a><a href='#L764'>764</a>
<a name='L765'></a><a href='#L765'>765</a>
<a name='L766'></a><a href='#L766'>766</a>
<a name='L767'></a><a href='#L767'>767</a>
<a name='L768'></a><a href='#L768'>768</a>
<a name='L769'></a><a href='#L769'>769</a>
<a name='L770'></a><a href='#L770'>770</a>
<a name='L771'></a><a href='#L771'>771</a>
<a name='L772'></a><a href='#L772'>772</a>
<a name='L773'></a><a href='#L773'>773</a>
<a name='L774'></a><a href='#L774'>774</a>
<a name='L775'></a><a href='#L775'>775</a>
<a name='L776'></a><a href='#L776'>776</a>
<a name='L777'></a><a href='#L777'>777</a>
<a name='L778'></a><a href='#L778'>778</a>
<a name='L779'></a><a href='#L779'>779</a>
<a name='L780'></a><a href='#L780'>780</a>
<a name='L781'></a><a href='#L781'>781</a>
<a name='L782'></a><a href='#L782'>782</a>
<a name='L783'></a><a href='#L783'>783</a>
<a name='L784'></a><a href='#L784'>784</a>
<a name='L785'></a><a href='#L785'>785</a>
<a name='L786'></a><a href='#L786'>786</a>
<a name='L787'></a><a href='#L787'>787</a>
<a name='L788'></a><a href='#L788'>788</a>
<a name='L789'></a><a href='#L789'>789</a>
<a name='L790'></a><a href='#L790'>790</a>
<a name='L791'></a><a href='#L791'>791</a>
<a name='L792'></a><a href='#L792'>792</a>
<a name='L793'></a><a href='#L793'>793</a>
<a name='L794'></a><a href='#L794'>794</a>
<a name='L795'></a><a href='#L795'>795</a>
<a name='L796'></a><a href='#L796'>796</a>
<a name='L797'></a><a href='#L797'>797</a>
<a name='L798'></a><a href='#L798'>798</a>
<a name='L799'></a><a href='#L799'>799</a>
<a name='L800'></a><a href='#L800'>800</a>
<a name='L801'></a><a href='#L801'>801</a>
<a name='L802'></a><a href='#L802'>802</a>
<a name='L803'></a><a href='#L803'>803</a></td><td class="line-coverage quiet"><span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">const DatabaseManager = <span class="cstat-no" title="statement not covered" >require('../database/DatabaseManager');</span>
const Logger = <span class="cstat-no" title="statement not covered" >require('../utils/Logger');</span>
const ErrorHandler = <span class="cstat-no" title="statement not covered" >require('../utils/ErrorHandler');</span>
const ConfigManager = <span class="cstat-no" title="statement not covered" >require('../config/ConfigManager');</span>
&nbsp;
class FleetController {
<span class="fstat-no" title="function not covered" >  co</span>nstructor() {
<span class="cstat-no" title="statement not covered" >    this.config = new ConfigManager(); </span>// Use an instance
&nbsp;
<span class="cstat-no" title="statement not covered" >    this.agents = new Map();</span>
<span class="cstat-no" title="statement not covered" >    this.maxAgents = this.config.get('maxAgents');</span>
<span class="cstat-no" title="statement not covered" >    this.minAgents = this.config.get('minAgents') || Math.floor(this.maxAgents * 0.7); </span>// Default minAgents if not set
<span class="cstat-no" title="statement not covered" >    this.targetUtilization = this.config.get('targetUtilization') || 0.8;</span>
<span class="cstat-no" title="statement not covered" >    this.healthCheckInterval = this.config.get('healthCheckInterval');</span>
<span class="cstat-no" title="statement not covered" >    this.scaleUpBatchSize = this.config.get('scaleUpBatchSize') || 5;</span>
<span class="cstat-no" title="statement not covered" >    this.scaleDownBatchSize = this.config.get('scaleDownBatchSize') || 3;</span>
<span class="cstat-no" title="statement not covered" >    this.initialized = false;</span>
<span class="cstat-no" title="statement not covered" >    this.maxRetries = this.config.get('maxRetries');</span>
<span class="cstat-no" title="statement not covered" >    this.retryDelay = this.config.get('retryDelay') || 5000;</span>
<span class="cstat-no" title="statement not covered" >    this.errorThreshold = this.config.get('errorThreshold') || 5;</span>
<span class="cstat-no" title="statement not covered" >    this.recoveryInterval = this.config.get('recoveryInterval') || 300000; </span>// 5 minutes
<span class="cstat-no" title="statement not covered" >    this.errorCounts = new Map();</span>
&nbsp;
    // Distributed deployment settings
<span class="cstat-no" title="statement not covered" >    this.distributedEnabled = this.config.get('distributedEnabled');</span>
<span class="cstat-no" title="statement not covered" >    this.nodeRole = this.config.get('nodeRole');</span>
<span class="cstat-no" title="statement not covered" >    this.masterNodeUrl = this.config.get('masterNodeUrl');</span>
<span class="cstat-no" title="statement not covered" >    this.nodePort = this.config.get('nodePort');</span>
<span class="cstat-no" title="statement not covered" >    this.nodeId = this.config.get('nodeId');</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    this.workerNodes = new Map(); </span>// For master to keep track of workers { id: { url, port, capacity, currentLoad, lastSeen }}
<span class="cstat-no" title="statement not covered" >    this.agentToWorkerMap = new Map(); </span>// For master to know which worker an agent belongs to
  }
&nbsp;
<span class="fstat-no" title="function not covered" >  as</span>ync initialize() {
<span class="cstat-no" title="statement not covered" >    try {</span>
<span class="cstat-no" title="statement not covered" >      Logger.info('Initializing Fleet Controller');</span>
<span class="cstat-no" title="statement not covered" >      Logger.info(`Distributed mode: ${this.distributedEnabled}, Role: ${this.nodeRole}`);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      if (this.distributedEnabled) {</span>
<span class="cstat-no" title="statement not covered" >        if (this.nodeRole === 'master') {</span>
<span class="cstat-no" title="statement not covered" >          Logger.info('Initializing as MASTER node.');</span>
<span class="cstat-no" title="statement not covered" >          await this.initializeMasterNode();</span>
          // Master node also performs standard initialization for its own managed agents (if any) or global state.
        } else <span class="cstat-no" title="statement not covered" >if (this.nodeRole === 'worker') {</span>
<span class="cstat-no" title="statement not covered" >          Logger.info(`Initializing as WORKER node. Master URL: ${this.masterNodeUrl}`);</span>
<span class="cstat-no" title="statement not covered" >          await this.initializeWorkerNode();</span>
          // Worker node initialization might be different, e.g., no global health checks or scaling decisions.
          // It will primarily manage agents assigned by the master.
          // Worker also needs to start its own health monitoring for its *local* agents
<span class="cstat-no" title="statement not covered" >          this.startHealthMonitoring(); </span>// Worker monitors its own agents
<span class="cstat-no" title="statement not covered" >          this.initialized = true;</span>
<span class="cstat-no" title="statement not covered" >          Logger.info('Worker Fleet Controller initialized.');</span>
<span class="cstat-no" title="statement not covered" >          return; </span>// Worker might not run the full standard initialization below immediately.
        } else {
<span class="cstat-no" title="statement not covered" >          Logger.warn(`Invalid NODE_ROLE: ${this.nodeRole}. Running in standalone mode.`);</span>
<span class="cstat-no" title="statement not covered" >          this.distributedEnabled = false; </span>// Fallback to non-distributed
        }
      }
&nbsp;
      // Standard initialization for standalone or master node
<span class="cstat-no" title="statement not covered" >      Logger.info('Performing standard Fleet Controller initialization.');</span>
      
      // Load existing agents from database
<span class="cstat-no" title="statement not covered" >      await this.loadExistingAgents();</span>
      
      // Start health monitoring
<span class="cstat-no" title="statement not covered" >      this.startHealthMonitoring();</span>
      
<span class="cstat-no" title="statement not covered" >      this.initialized = true;</span>
<span class="cstat-no" title="statement not covered" >      Logger.info('Fleet Controller initialized successfully', {</span>
        maxAgents: this.maxAgents,
        minAgents: this.minAgents,
        targetUtilization: this.targetUtilization
      });
    } catch (error) {
<span class="cstat-no" title="statement not covered" >      Logger.error('Failed to initialize Fleet Controller', { error });</span>
<span class="cstat-no" title="statement not covered" >      throw error;</span>
    }
  }
&nbsp;
<span class="fstat-no" title="function not covered" >  as</span>ync initializeMasterNode() {
    // Placeholder for master node specific initialization
    // e.g., start an HTTP server to listen for worker registrations and commands
<span class="cstat-no" title="statement not covered" >    Logger.info(`Master node (${this.nodeId}) specific initialization started.`);</span>
<span class="cstat-no" title="statement not covered" >    this.workerNodes.clear();</span>
<span class="cstat-no" title="statement not covered" >    this.agentToWorkerMap.clear();</span>
<span class="cstat-no" title="statement not covered" >    Logger.info(`Master node (${this.nodeId}) ready to accept worker registrations and manage the fleet.`);</span>
    // TODO: Implement HTTP server for worker communication (e.g., Express app on this.nodePort if master uses it for this)
    // Example endpoint: POST /register-worker { workerId, address, port, capacity }
    // Example endpoint: POST /worker-heartbeat { workerId, currentLoad, status }
<span class="cstat-no" title="statement not covered" >    Logger.info(`Master node (${this.nodeId}) specific initialization complete.`);</span>
  }
&nbsp;
  // Method for master to handle worker registration
<span class="fstat-no" title="function not covered" >  ha</span>ndleWorkerRegistration(workerId, workerInfo) {
<span class="cstat-no" title="statement not covered" >    if (this.nodeRole !== 'master') <span class="cstat-no" title="statement not covered" >return;</span></span>
<span class="cstat-no" title="statement not covered" >    Logger.info(`Master node (${this.nodeId}) received registration from worker: ${workerId}`, { workerInfo });</span>
<span class="cstat-no" title="statement not covered" >    this.workerNodes.set(workerId, {</span>
      ...workerInfo, // e.g., { address, port, capacity }
      currentLoad: 0,
      lastSeen: Date.now(),
      status: 'registered'
    });
  }
&nbsp;
  // Placeholder for master to handle worker heartbeat
<span class="fstat-no" title="function not covered" >  ha</span>ndleWorkerHeartbeat(workerId, heartbeatData) {
<span class="cstat-no" title="statement not covered" >    if (this.nodeRole !== 'master') <span class="cstat-no" title="statement not covered" >return;</span></span>
<span class="cstat-no" title="statement not covered" >    if (!this.workerNodes.has(workerId)) {</span>
<span class="cstat-no" title="statement not covered" >      Logger.warn(`Master node (${this.nodeId}) received heartbeat from unknown worker: ${workerId}`);</span>
      // Optionally, could trigger re-registration or ignore
<span class="cstat-no" title="statement not covered" >      return;</span>
    }
<span class="cstat-no" title="statement not covered" >    this.workerNodes.set(workerId, {</span>
      ...this.workerNodes.get(workerId),
      ...heartbeatData, // e.g., { currentLoad, status }
      lastSeen: Date.now()
    });
<span class="cstat-no" title="statement not covered" >    Logger.debug(`Master node (${this.nodeId}) received heartbeat from worker: ${workerId}`, { heartbeatData });</span>
  }
&nbsp;
<span class="fstat-no" title="function not covered" >  as</span>ync initializeWorkerNode() {
    // Placeholder for worker node specific initialization
    // e.g., register with the master node, setup communication channel
<span class="cstat-no" title="statement not covered" >    Logger.info(`Worker node (${this.nodeId}) specific initialization started.`);</span>
<span class="cstat-no" title="statement not covered" >    try {</span>
      // Simulate registration details that the worker would send to the master.
      const registrationDetails = <span class="cstat-no" title="statement not covered" >{</span>
        workerId: this.nodeId,
        address: `http://worker-address-placeholder:${this.nodePort}`, // Worker needs to know its own reachable address
        port: this.nodePort, // Port worker listens on for commands from master
        capacity: this.config.get('maxAgents') // Worker's own capacity for agents
      };
<span class="cstat-no" title="statement not covered" >      Logger.info(`Worker node (${this.nodeId}) registration details (simulated send to master):`, registrationDetails);</span>
<span class="cstat-no" title="statement not covered" >      Logger.info(`Worker node (${this.nodeId}) would now attempt to call master.handleWorkerRegistration with these details.`);</span>
&nbsp;
      // Simulate sending periodic heartbeats to the master.
      // This is conceptual; in a real system, this would be a timer initiating network calls.
<span class="cstat-no" title="statement not covered" >      Logger.info(`Worker node (${this.nodeId}) would periodically send heartbeats to the master with its current load (this.agents.size) and status ('active'). Master would call its handleWorkerHeartbeat.`);</span>
      
      // TODO: Implement actual network call for registration to master (`this.masterNodeUrl`).
      // TODO: Implement actual network calls for periodic heartbeats to master.
      // TODO: Worker should start an HTTP server (if applicable) to listen for commands from the master on its `this.nodePort`.
      //       e.g., app.listen(this.nodePort, () =&gt; Logger.info(`Worker ${this.nodeId} listening on ${this.nodePort} for master commands`));
&nbsp;
    } catch (error) {
<span class="cstat-no" title="statement not covered" >      Logger.error(`Worker node (${this.nodeId}) failed during its initialization process, potentially during simulated registration or setup.`, { error: error.message, masterUrl: this.masterNodeUrl });</span>
      // Depending on the error, might rethrow or attempt recovery.
      // For now, rethrowing to indicate critical failure during init.
<span class="cstat-no" title="statement not covered" >      throw new Error(`Worker (${this.nodeId}) failed to complete specific initialization steps: ${error.message}`);</span>
    }
<span class="cstat-no" title="statement not covered" >    Logger.info(`Worker node (${this.nodeId}) specific initialization complete. Ready to receive tasks and manage local agents.`);</span>
  }
&nbsp;
<span class="fstat-no" title="function not covered" >  as</span>ync loadExistingAgents() {
<span class="cstat-no" title="statement not covered" >    try {</span>
      const agents = <span class="cstat-no" title="statement not covered" >await DatabaseManager.getAgentsByStatus('idle');</span>
<span class="cstat-no" title="statement not covered" >      agents.forEach(<span class="fstat-no" title="function not covered" >ag</span>ent =&gt; {</span>
<span class="cstat-no" title="statement not covered" >        this.agents.set(agent.id, {</span>
          status: agent.status,
          streamId: agent.stream_id,
          lastUpdated: new Date(agent.updated_at)
        });
      });
<span class="cstat-no" title="statement not covered" >      Logger.info('Loaded existing agents', { count: agents.length });</span>
    } catch (error) {
<span class="cstat-no" title="statement not covered" >      throw ErrorHandler.createCustomError(</span>
        'FleetControllerError',
        'Failed to load existing agents',
        { originalError: error.message }
      );
    }
  }
&nbsp;
<span class="fstat-no" title="function not covered" >  st</span>artHealthMonitoring() {
<span class="cstat-no" title="statement not covered" >    this.healthCheckTimer = setInterval(<span class="fstat-no" title="function not covered" >as</span>ync () =&gt; {</span>
<span class="cstat-no" title="statement not covered" >      try {</span>
<span class="cstat-no" title="statement not covered" >        await this.performHealthCheck();</span>
      } catch (error) {
<span class="cstat-no" title="statement not covered" >        Logger.error('Health check failed', { error });</span>
      }
    }, this.healthCheckInterval);
  }
&nbsp;
<span class="fstat-no" title="function not covered" >  as</span>ync performHealthCheck() {
    const metrics = <span class="cstat-no" title="statement not covered" >{</span>
      totalAgents: this.agents.size,
      activeAgents: 0,
      idleAgents: 0,
      errorAgents: 0
    };
&nbsp;
<span class="cstat-no" title="statement not covered" >    for (const [agentId, agent] of this.agents) {</span>
<span class="cstat-no" title="statement not covered" >      switch (agent.status) {</span>
        case 'watching':
<span class="cstat-no" title="statement not covered" >          metrics.activeAgents++;</span>
<span class="cstat-no" title="statement not covered" >          break;</span>
        case 'idle':
<span class="cstat-no" title="statement not covered" >          metrics.idleAgents++;</span>
<span class="cstat-no" title="statement not covered" >          break;</span>
        case 'error':
<span class="cstat-no" title="statement not covered" >          metrics.errorAgents++;</span>
<span class="cstat-no" title="statement not covered" >          await this.handleErroredAgent(agentId, agent);</span>
<span class="cstat-no" title="statement not covered" >          break;</span>
      }
    }
&nbsp;
    // Record metrics
<span class="cstat-no" title="statement not covered" >    await DatabaseManager.recordMetric('fleet_health', 1, metrics);</span>
    
    // Check if we need to scale the fleet (Only master should do this, or standalone)
<span class="cstat-no" title="statement not covered" >    if (this.nodeRole === 'master' || !this.distributedEnabled) {</span>
<span class="cstat-no" title="statement not covered" >      await this.adjustFleetSize(metrics);</span>
    }
&nbsp;
    // Run error recovery if needed
<span class="cstat-no" title="statement not covered" >    if (metrics.errorAgents &gt; 0) {</span>
<span class="cstat-no" title="statement not covered" >      await this.runErrorRecovery(metrics);</span>
    }
  }
&nbsp;
<span class="fstat-no" title="function not covered" >  as</span>ync handleErroredAgent(agentId, agent) {
    const errorCount = <span class="cstat-no" title="statement not covered" >this.errorCounts.get(agentId) || 0;</span>
<span class="cstat-no" title="statement not covered" >    this.errorCounts.set(agentId, errorCount + 1);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    if (errorCount &gt;= this.maxRetries) {</span>
<span class="cstat-no" title="statement not covered" >      Logger.warn('Agent exceeded max retries, destroying', { agentId, errorCount });</span>
<span class="cstat-no" title="statement not covered" >      await this.destroyAgent(agentId);</span>
<span class="cstat-no" title="statement not covered" >      this.errorCounts.delete(agentId);</span>
    } else {
<span class="cstat-no" title="statement not covered" >      await this.retryAgent(agentId, agent);</span>
    }
  }
&nbsp;
<span class="fstat-no" title="function not covered" >  as</span>ync retryAgent(agentId, agent) {
<span class="cstat-no" title="statement not covered" >    try {</span>
<span class="cstat-no" title="statement not covered" >      Logger.info('Attempting to recover agent', { agentId });</span>
      
      // Log the retry attempt
<span class="cstat-no" title="statement not covered" >      await DatabaseManager.logAgentEvent(agentId, 'retry_attempt', {</span>
        retryCount: this.errorCounts.get(agentId),
        previousStatus: agent.status,
        streamId: agent.streamId
      });
&nbsp;
      // Reset the agent to idle state
<span class="cstat-no" title="statement not covered" >      await this.updateAgentStatus(agentId, 'idle', null);</span>
&nbsp;
      // Wait for retry delay
<span class="cstat-no" title="statement not covered" >      await new Promise(<span class="fstat-no" title="function not covered" >re</span>solve =&gt; <span class="cstat-no" title="statement not covered" >setTimeout(resolve, this.retryDelay))</span>;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      Logger.info('Agent recovery attempt completed', { agentId });</span>
    } catch (error) {
<span class="cstat-no" title="statement not covered" >      Logger.error('Agent recovery attempt failed', {</span>
        agentId,
        error: error.message,
        retryCount: this.errorCounts.get(agentId)
      });
    }
  }
&nbsp;
<span class="fstat-no" title="function not covered" >  as</span>ync runErrorRecovery(metrics) {
<span class="cstat-no" title="statement not covered" >    try {</span>
      // If too many agents are in error state, trigger emergency recovery
<span class="cstat-no" title="statement not covered" >      if (metrics.errorAgents &gt; this.errorThreshold) {</span>
<span class="cstat-no" title="statement not covered" >        Logger.warn('Emergency recovery triggered', {</span>
          errorAgents: metrics.errorAgents,
          threshold: this.errorThreshold
        });
&nbsp;
        // Log the emergency recovery event
<span class="cstat-no" title="statement not covered" >        await DatabaseManager.logAgentEvent('fleet', 'emergency_recovery', {</span>
          errorAgents: metrics.errorAgents,
          totalAgents: metrics.totalAgents
        });
&nbsp;
        // Get all errored agents
        const erroredAgents = <span class="cstat-no" title="statement not covered" >Array.from(this.agents.entries())</span>
          .filter(<span class="fstat-no" title="function not covered" >([</span>_, agent]) =&gt; <span class="cstat-no" title="statement not covered" >agent.status === 'error')</span>
          .map(<span class="fstat-no" title="function not covered" >([</span>agentId]) =&gt; <span class="cstat-no" title="statement not covered" >agentId)</span>;
&nbsp;
        // Destroy and recreate errored agents
<span class="cstat-no" title="statement not covered" >        for (const agentId of erroredAgents) {</span>
<span class="cstat-no" title="statement not covered" >          await this.destroyAgent(agentId);</span>
<span class="cstat-no" title="statement not covered" >          await this.createAgent();</span>
        }
&nbsp;
        // Clear error counts
<span class="cstat-no" title="statement not covered" >        this.errorCounts.clear();</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        Logger.info('Emergency recovery completed', {</span>
          agentsRecreated: erroredAgents.length
        });
      }
    } catch (error) {
<span class="cstat-no" title="statement not covered" >      Logger.error('Error recovery failed', { error: error.message });</span>
    }
  }
&nbsp;
<span class="fstat-no" title="function not covered" >  as</span>ync adjustFleetSize(metrics) {
    const currentUtilization = <span class="cstat-no" title="statement not covered" >metrics.activeAgents / this.maxAgents;</span>
    const utilizationGap = <span class="cstat-no" title="statement not covered" >Math.abs(currentUtilization - this.targetUtilization);</span>
    
    // Only scale if the gap is significant (more than 5%)
    // And if this node is responsible for scaling (master or standalone)
<span class="cstat-no" title="statement not covered" >    if (utilizationGap &lt; 0.05 || (this.distributedEnabled &amp;&amp; this.nodeRole !== 'master')) {</span>
<span class="cstat-no" title="statement not covered" >      return;</span>
    }
    
<span class="cstat-no" title="statement not covered" >    if (currentUtilization &gt; this.targetUtilization &amp;&amp; metrics.totalAgents &lt; this.maxAgents) {</span>
<span class="cstat-no" title="statement not covered" >      await this.scaleUp(metrics);</span>
    } else <span class="cstat-no" title="statement not covered" >if (currentUtilization &lt; this.targetUtilization &amp;&amp; metrics.totalAgents &gt; this.minAgents) {</span>
<span class="cstat-no" title="statement not covered" >      await this.scaleDown(metrics);</span>
    }
&nbsp;
    // Log the scaling decision
<span class="cstat-no" title="statement not covered" >    await DatabaseManager.recordMetric('fleet_scaling', currentUtilization, {</span>
      totalAgents: metrics.totalAgents,
      activeAgents: metrics.activeAgents,
      targetUtilization: this.targetUtilization
    });
  }
&nbsp;
<span class="fstat-no" title="function not covered" >  as</span>ync scaleUp(metrics) {
<span class="cstat-no" title="statement not covered" >    try {</span>
      // Calculate how many agents to add
      const agentsToAdd = <span class="cstat-no" title="statement not covered" >Math.min(</span>
        this.scaleUpBatchSize,
        this.maxAgents - metrics.totalAgents
      );
&nbsp;
<span class="cstat-no" title="statement not covered" >      Logger.info('Scaling up fleet', {</span>
        currentAgents: metrics.totalAgents,
        agentsToAdd,
        maxAgents: this.maxAgents
      });
&nbsp;
      // Create new agents in parallel
      const creationPromises = <span class="cstat-no" title="statement not covered" >Array(agentsToAdd)</span>
        .fill(null)
        .map(<span class="fstat-no" title="function not covered" >()</span> =&gt; <span class="cstat-no" title="statement not covered" >this.createAgent())</span>;
&nbsp;
      const newAgentIds = <span class="cstat-no" title="statement not covered" >await Promise.all(creationPromises);</span>
&nbsp;
      // Log the scale-up event
<span class="cstat-no" title="statement not covered" >      await DatabaseManager.logAgentEvent('fleet', 'scale_up', {</span>
        agentsAdded: agentsToAdd,
        newAgentIds,
        totalAgents: metrics.totalAgents + agentsToAdd
      });
&nbsp;
<span class="cstat-no" title="statement not covered" >      return newAgentIds;</span>
    } catch (error) {
<span class="cstat-no" title="statement not covered" >      throw ErrorHandler.createCustomError(</span>
        'FleetControllerError',
        'Failed to scale up fleet',
        { originalError: error.message }
      );
    }
  }
&nbsp;
<span class="fstat-no" title="function not covered" >  as</span>ync scaleDown(metrics) {
<span class="cstat-no" title="statement not covered" >    try {</span>
      // Calculate how many agents to remove
      const agentsToRemove = <span class="cstat-no" title="statement not covered" >Math.min(</span>
        this.scaleDownBatchSize,
        metrics.totalAgents - this.minAgents
      );
&nbsp;
<span class="cstat-no" title="statement not covered" >      Logger.info('Scaling down fleet', {</span>
        currentAgents: metrics.totalAgents,
        agentsToRemove,
        minAgents: this.minAgents
      });
&nbsp;
      // Find idle agents to remove
      const idleAgents = <span class="cstat-no" title="statement not covered" >Array.from(this.agents.entries())</span>
        .filter(<span class="fstat-no" title="function not covered" >([</span>_, agent]) =&gt; <span class="cstat-no" title="statement not covered" >agent.status === 'idle')</span>
        .slice(0, agentsToRemove)
        .map(<span class="fstat-no" title="function not covered" >([</span>agentId]) =&gt; <span class="cstat-no" title="statement not covered" >agentId)</span>;
&nbsp;
      // If not enough idle agents, find agents with lowest activity
<span class="cstat-no" title="statement not covered" >      if (idleAgents.length &lt; agentsToRemove) {</span>
        const activeAgents = <span class="cstat-no" title="statement not covered" >await DatabaseManager.getAgentsByStatus('watching');</span>
        const sortedByActivity = <span class="cstat-no" title="statement not covered" >activeAgents</span>
          .sort(<span class="fstat-no" title="function not covered" >(a</span>, b) =&gt; <span class="cstat-no" title="statement not covered" >a.watch_time - b.watch_time)</span>
          .slice(0, agentsToRemove - idleAgents.length)
          .map(<span class="fstat-no" title="function not covered" >ag</span>ent =&gt; <span class="cstat-no" title="statement not covered" >agent.id)</span>;
        
<span class="cstat-no" title="statement not covered" >        idleAgents.push(...sortedByActivity);</span>
      }
&nbsp;
      // Destroy agents in parallel
      const destructionPromises = <span class="cstat-no" title="statement not covered" >idleAgents.map(<span class="fstat-no" title="function not covered" >ag</span>entId =&gt; <span class="cstat-no" title="statement not covered" >this.destroyAgent(agentId))</span>;</span>
<span class="cstat-no" title="statement not covered" >      await Promise.all(destructionPromises);</span>
&nbsp;
      // Log the scale-down event
<span class="cstat-no" title="statement not covered" >      await DatabaseManager.logAgentEvent('fleet', 'scale_down', {</span>
        agentsRemoved: idleAgents.length,
        removedAgentIds: idleAgents,
        totalAgents: metrics.totalAgents - idleAgents.length
      });
&nbsp;
<span class="cstat-no" title="statement not covered" >      return idleAgents;</span>
    } catch (error) {
<span class="cstat-no" title="statement not covered" >      throw ErrorHandler.createCustomError(</span>
        'FleetControllerError',
        'Failed to scale down fleet',
        { originalError: error.message }
      );
    }
  }
&nbsp;
<span class="fstat-no" title="function not covered" >  as</span>ync createAgent() {
    // For non-master nodes (workers or standalone), check against their local maxAgents.
    // Master's ability to create locally (if no workers) is also bound by its maxAgents.
    // The overall fleet capacity is managed by the sum of worker capacities + master's local capacity.
<span class="cstat-no" title="statement not covered" >    if (this.agents.size &gt;= this.maxAgents &amp;&amp; (!this.distributedEnabled || this.nodeRole !== 'master' || (this.distributedEnabled &amp;&amp; this.nodeRole === 'master' &amp;&amp; this.workerNodes.size === 0))) {</span>
<span class="cstat-no" title="statement not covered" >      Logger.warn(`Node ${this.nodeId} (Role: ${this.nodeRole}) reached its local agent limit of ${this.maxAgents}. Cannot create new agent locally.`);</span>
<span class="cstat-no" title="statement not covered" >      throw ErrorHandler.createCustomError(</span>
        'FleetControllerError',
        `Node ${this.nodeId} agent limit reached for local creation`
      );
    }
    
    const agentId = <span class="cstat-no" title="statement not covered" >`agent-${Date.now()}-${Math.random().toString(36).substring(2, 7)}`;</span>
    
<span class="cstat-no" title="statement not covered" >    if (this.distributedEnabled &amp;&amp; this.nodeRole === 'master') {</span>
      const selectedWorker = <span class="cstat-no" title="statement not covered" >this.selectWorkerForNewAgent();</span>
<span class="cstat-no" title="statement not covered" >      if (selectedWorker) {</span>
<span class="cstat-no" title="statement not covered" >        Logger.info(`Master node (${this.nodeId}) selected worker ${selectedWorker.id} (Node: ${selectedWorker.nodeId || 'N/A'}) for new agent ${agentId}.`);</span>
<span class="cstat-no" title="statement not covered" >        this.agentToWorkerMap.set(agentId, selectedWorker.id);</span>
        
<span class="cstat-no" title="statement not covered" >        try {</span>
          // Master creates a placeholder record for tracking the delegation.
<span class="cstat-no" title="statement not covered" >          await DatabaseManager.createAgent(agentId, this.nodeId, { </span>
            status: 'pending_delegation', 
            assigned_worker_id: selectedWorker.id 
          });
<span class="cstat-no" title="statement not covered" >          await DatabaseManager.logAgentEvent(agentId, 'delegation_initiated', { </span>
            masterNodeId: this.nodeId, 
            workerId: selectedWorker.id,
            workerNodeId: selectedWorker.nodeId || 'N/A',
            targetAgentId: agentId
          });
<span class="cstat-no" title="statement not covered" >          Logger.info(`Master node (${this.nodeId}) initiated delegation of agent ${agentId} to worker ${selectedWorker.id}.`);</span>
          
          // CONCEPTUAL RPC CALL: Master commands worker to create agent
          // In a real system: await this.rpcCallToWorker(selectedWorker.id, 'createAgent', { agentId, streamConfig, masterNodeId: this.nodeId });
          // Worker's handleMasterCommand_CreateAgent would be invoked.
<span class="cstat-no" title="statement not covered" >          Logger.info(`Master node (${this.nodeId}) (simulated) RPC: Commanding worker ${selectedWorker.id} to execute handleMasterCommand_CreateAgent for agent ${agentId}.`);</span>
&nbsp;
          // Optimistically update worker's load. Worker would confirm via heartbeat/report.
          const workerData = <span class="cstat-no" title="statement not covered" >this.workerNodes.get(selectedWorker.id);</span>
<span class="cstat-no" title="statement not covered" >          if(workerData) {</span>
<span class="cstat-no" title="statement not covered" >            workerData.currentLoad = (workerData.currentLoad || 0) + 1;</span>
<span class="cstat-no" title="statement not covered" >            this.workerNodes.set(selectedWorker.id, workerData);</span>
<span class="cstat-no" title="statement not covered" >            Logger.debug(`Master optimistically updated load for worker ${selectedWorker.id} to ${workerData.currentLoad}/${workerData.capacity}`);</span>
          }
          
<span class="cstat-no" title="statement not covered" >          return agentId; </span>// Master's role for this agentId creation is done (delegation initiated).
        } catch (dbError) {
<span class="cstat-no" title="statement not covered" >          Logger.error(`Master node (${this.nodeId}) failed to create/log delegation placeholder for agent ${agentId} to worker ${selectedWorker.id}`, { error: dbError.message });</span>
<span class="cstat-no" title="statement not covered" >          this.agentToWorkerMap.delete(agentId); </span>// Rollback map
<span class="cstat-no" title="statement not covered" >          throw ErrorHandler.createCustomError(</span>
            'FleetControllerError',
            `Master failed to record/log delegation for ${agentId}: ${dbError.message}`,
            { originalError: dbError.message }
          );
        }
      } else {
<span class="cstat-no" title="statement not covered" >        Logger.warn(`Master node (${this.nodeId}): No available/suitable workers. Attempting to create agent ${agentId} locally on master.`);</span>
        // Fall through to local creation by master if it has capacity.
      }
    }
&nbsp;
    // Local creation logic (for standalone, worker, or master fallback/local agents)
<span class="cstat-no" title="statement not covered" >    return this._createAgentLocally(agentId);</span>
  }
&nbsp;
<span class="fstat-no" title="function not covered" >  as</span>ync _createAgentLocally(agentId, streamConfig = <span class="branch-0 cbranch-no" title="branch not covered" >null)</span> { // streamConfig might be used later by agent
    // This node (worker, or master doing local creation) is creating the agent.
<span class="cstat-no" title="statement not covered" >    Logger.info(`Node ${this.nodeId} (Role: ${this.nodeRole}) creating agent ${agentId} locally.`);</span>
<span class="cstat-no" title="statement not covered" >    try {</span>
      // The primary DB record for the agent is created by the node hosting it.
<span class="cstat-no" title="statement not covered" >      await DatabaseManager.createAgent(agentId, this.nodeId, { status: 'idle' });</span>
<span class="cstat-no" title="statement not covered" >      this.agents.set(agentId, {</span>
        id: agentId, // Storing id for easier access
        status: 'idle',
        streamId: null, // streamConfig could set this if provided
        lastUpdated: new Date(),
        nodeId: this.nodeId 
      });
<span class="cstat-no" title="statement not covered" >      await DatabaseManager.logAgentEvent(agentId, 'created_locally', { nodeId: this.nodeId });</span>
<span class="cstat-no" title="statement not covered" >      Logger.info(`Agent ${agentId} created successfully on node ${this.nodeId}.`);</span>
<span class="cstat-no" title="statement not covered" >      return agentId;</span>
    } catch (error) {
<span class="cstat-no" title="statement not covered" >      Logger.error(`Failed to create agent ${agentId} locally on node ${this.nodeId}`, { error: error.message });</span>
<span class="cstat-no" title="statement not covered" >      throw ErrorHandler.createCustomError(</span>
        'FleetControllerError',
        `Failed to create agent ${agentId} locally on ${this.nodeId}: ${error.message}`,
        { originalError: error.message }
      );
    }
  }
&nbsp;
<span class="fstat-no" title="function not covered" >  as</span>ync destroyAgent(agentId, reason = <span class="branch-0 cbranch-no" title="branch not covered" >'manual_shutdown')</span> {
<span class="cstat-no" title="statement not covered" >    if (this.distributedEnabled &amp;&amp; this.nodeRole === 'master') {</span>
      const workerId = <span class="cstat-no" title="statement not covered" >this.agentToWorkerMap.get(agentId);</span>
<span class="cstat-no" title="statement not covered" >      if (workerId) {</span>
        const workerData = <span class="cstat-no" title="statement not covered" >this.workerNodes.get(workerId);</span>
<span class="cstat-no" title="statement not covered" >        if (workerData) {</span>
<span class="cstat-no" title="statement not covered" >          Logger.info(`Master node (${this.nodeId}) commanding worker ${workerId} (Node: ${workerData.nodeId || 'N/A'}) to destroy agent ${agentId}. Reason: ${reason}`);</span>
<span class="cstat-no" title="statement not covered" >          try {</span>
            // Master updates its placeholder/delegation record status.
<span class="cstat-no" title="statement not covered" >            await DatabaseManager.updateAgentStatus(agentId, 'terminating_delegated', null, this.nodeId); </span>// Master updates its view of agent state
<span class="cstat-no" title="statement not covered" >            await DatabaseManager.logAgentEvent(agentId, 'delegated_destruction_initiated', { </span>
              masterNodeId: this.nodeId, 
              workerId: workerId,
              workerNodeId: workerData.nodeId || 'N/A',
              targetAgentId: agentId,
              reason
            });
&nbsp;
            // CONCEPTUAL RPC CALL: Master commands worker to destroy agent
            // In a real system: await this.rpcCallToWorker(workerId, 'destroyAgent', { agentId, reason, masterNodeId: this.nodeId });
            // Worker's handleMasterCommand_DestroyAgent would be invoked.
<span class="cstat-no" title="statement not covered" >            Logger.info(`Master node (${this.nodeId}) (simulated) RPC: Commanding worker ${workerId} to execute handleMasterCommand_DestroyAgent for agent ${agentId}.`);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >            this.agentToWorkerMap.delete(agentId);</span>
<span class="cstat-no" title="statement not covered" >            if (workerData.currentLoad &gt; 0) <span class="cstat-no" title="statement not covered" >workerData.currentLoad--;</span></span>
<span class="cstat-no" title="statement not covered" >            this.workerNodes.set(workerId, workerData); </span>// Update worker load
<span class="cstat-no" title="statement not covered" >            Logger.debug(`Master optimistically updated load for worker ${workerId} to ${workerData.currentLoad}/${workerData.capacity} after destroy command.`);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >            Logger.info(`Master node (${this.nodeId}) completed delegation steps for destroying agent ${agentId} on worker ${workerId}.`);</span>
<span class="cstat-no" title="statement not covered" >            return; </span>// Master's job for this delegated agent is done (delegation command sent).
          } catch (dbError) {
<span class="cstat-no" title="statement not covered" >            Logger.error(`Master node (${this.nodeId}) failed to update/log delegation status for destroying agent ${agentId}`, { error: dbError.message });</span>
<span class="cstat-no" title="statement not covered" >            throw ErrorHandler.createCustomError(</span>
                'FleetControllerError',
                `Master failed to record/log delegated destruction for ${agentId}: ${dbError.message}`,
                { originalError: dbError.message }
            );
          }
        } else {
<span class="cstat-no" title="statement not covered" >          Logger.warn(`Master node (${this.nodeId}): Worker ${workerId} (for agent ${agentId}) not found in workerNodes map. Removing from agentToWorkerMap. Agent might be orphaned or already destroyed.`);</span>
<span class="cstat-no" title="statement not covered" >          this.agentToWorkerMap.delete(agentId); </span>// Clean up the map anyway.
          // Do not fall through to local destruction by master unless master is sure it owns it.
          // If the agent truly existed on a worker that disappeared, it's an orphan scenario for the worker.
          // Master has done its part by removing the mapping.
<span class="cstat-no" title="statement not covered" >          return; </span>// Avoid master trying to locally destroy an agent it doesn't own.
        }
      }
      // If agentId was not in agentToWorkerMap, it implies it's either a local agent on master or unknown.
      // Fall through to local destruction by master.
<span class="cstat-no" title="statement not covered" >      Logger.info(`Agent ${agentId} not found in agentToWorkerMap. Assuming it is a local agent on master or unknown. Proceeding with local destruction attempt.`);</span>
    }
&nbsp;
    // Local destruction logic (for standalone, worker, or master's own agents)
<span class="cstat-no" title="statement not covered" >    return this._destroyAgentLocally(agentId, reason);</span>
  }
&nbsp;
<span class="fstat-no" title="function not covered" >  as</span>ync _destroyAgentLocally(agentId, reason = <span class="branch-0 cbranch-no" title="branch not covered" >'manual_shutdown')</span> {
    // This node (worker, or master doing local destruction) is destroying the agent.
    const agentInfo = <span class="cstat-no" title="statement not covered" >this.agents.get(agentId);</span>
<span class="cstat-no" title="statement not covered" >    if (!agentInfo) {</span>
<span class="cstat-no" title="statement not covered" >      Logger.warn(`Node ${this.nodeId} (Role: ${this.nodeRole}) attempted to destroy non-existent local agent ${agentId}.`);</span>
<span class="cstat-no" title="statement not covered" >      return;</span>
    }
&nbsp;
<span class="cstat-no" title="statement not covered" >    Logger.info(`Node ${this.nodeId} (Role: ${this.nodeRole}) destroying agent ${agentId} locally. Reason: ${reason}`);</span>
<span class="cstat-no" title="statement not covered" >    try {</span>
      // TODO: Implement actual agent destruction (e.g., closing browser instance via AgentManager.destroy(agentId))
<span class="cstat-no" title="statement not covered" >      await DatabaseManager.logAgentEvent(agentId, 'destroyed_locally', { reason, nodeId: this.nodeId });</span>
<span class="cstat-no" title="statement not covered" >      await DatabaseManager.updateAgentStatus(agentId, 'terminated', null, this.nodeId); </span>// ensure status is updated for this node's record
<span class="cstat-no" title="statement not covered" >      this.agents.delete(agentId);</span>
<span class="cstat-no" title="statement not covered" >      this.errorCounts.delete(agentId);</span>
<span class="cstat-no" title="statement not covered" >      Logger.info(`Agent ${agentId} destroyed successfully locally on node ${this.nodeId}.`);</span>
    } catch (error) {
<span class="cstat-no" title="statement not covered" >      Logger.error(`Failed to destroy agent ${agentId} locally on node ${this.nodeId}`, { error: error.message });</span>
<span class="cstat-no" title="statement not covered" >      throw ErrorHandler.createCustomError(</span>
        'FleetControllerError',
        `Failed to destroy agent ${agentId} locally on ${this.nodeId}: ${error.message}`,
        { originalError: error.message }
      );
    }
  }
&nbsp;
<span class="fstat-no" title="function not covered" >  as</span>ync updateAgentStatus(agentId, status, streamId = <span class="branch-0 cbranch-no" title="branch not covered" >null)</span> {
    const agent = <span class="cstat-no" title="statement not covered" >this.agents.get(agentId);</span>
<span class="cstat-no" title="statement not covered" >    if (!agent) {</span>
      // If this is a master node, the agent might be on a worker.
<span class="cstat-no" title="statement not covered" >      if (this.distributedEnabled &amp;&amp; this.nodeRole === 'master' &amp;&amp; this.agentToWorkerMap.has(agentId)) {</span>
        const workerId = <span class="cstat-no" title="statement not covered" >this.agentToWorkerMap.get(agentId);</span>
<span class="cstat-no" title="statement not covered" >        Logger.info(`Master node (${this.nodeId}) received updateAgentStatus for agent ${agentId} (on worker ${workerId}). Master will update its placeholder for this agent if necessary. Original update should come from worker heartbeat/event.`);</span>
        // Master updates its own DB record if it maintains one for delegated agents.
<span class="cstat-no" title="statement not covered" >        try {</span>
<span class="cstat-no" title="statement not covered" >            await DatabaseManager.updateAgentStatus(agentId, status, streamId, this.nodeId); </span>// Master updates its view
<span class="cstat-no" title="statement not covered" >            Logger.info(`Master node (${this.nodeId}) updated its tracked status for delegated agent ${agentId} to ${status}.`);</span>
        } catch(dbError) {
<span class="cstat-no" title="statement not covered" >            Logger.error(`Master node (${this.nodeId}) failed to update its tracked status for delegated agent ${agentId}`, { error: dbError.message });</span>
        }
<span class="cstat-no" title="statement not covered" >        return; </span>// Master does not manage this.agents entry for delegated agents.
      }
<span class="cstat-no" title="statement not covered" >      throw ErrorHandler.createCustomError(</span>
        'FleetControllerError',
        'Agent not found',
        { agentId }
      );
    }
&nbsp;
<span class="cstat-no" title="statement not covered" >    try {</span>
<span class="cstat-no" title="statement not covered" >      await DatabaseManager.updateAgentStatus(agentId, status, streamId);</span>
<span class="cstat-no" title="statement not covered" >      this.agents.set(agentId, {</span>
        status,
        streamId,
        lastUpdated: new Date()
      });
      
<span class="cstat-no" title="statement not covered" >      Logger.info('Agent status updated', { agentId, status, streamId });</span>
    } catch (error) {
<span class="cstat-no" title="statement not covered" >      throw ErrorHandler.createCustomError(</span>
        'FleetControllerError',
        'Failed to update agent status',
        { agentId, status, streamId, originalError: error.message }
      );
    }
  }
&nbsp;
<span class="fstat-no" title="function not covered" >  cl</span>eanup() {
<span class="cstat-no" title="statement not covered" >    if (this.healthCheckTimer) {</span>
<span class="cstat-no" title="statement not covered" >      clearInterval(this.healthCheckTimer);</span>
    }
<span class="cstat-no" title="statement not covered" >    Logger.info('Fleet Controller cleanup completed');</span>
  }
&nbsp;
  // Placeholder for master to select a worker (simple round-robin or load-based)
<span class="fstat-no" title="function not covered" >  se</span>lectWorkerForNewAgent() {
    // Simple strategy: pick worker with fewest agents (lowest currentLoad)
    // More sophisticated: consider capacity, resource usage, etc.
    let bestWorker = <span class="cstat-no" title="statement not covered" >null;</span>
    let minLoadScore = <span class="cstat-no" title="statement not covered" >Infinity;</span> // Lower is better, based on load ratio and available capacity
&nbsp;
<span class="cstat-no" title="statement not covered" >    Logger.debug(`Selecting worker for new agent. Master (${this.nodeId}) evaluating ${this.workerNodes.size} registered workers.`);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    for (const [workerId, workerData] of this.workerNodes) {</span>
<span class="cstat-no" title="statement not covered" >      Logger.debug(`Master (${this.nodeId}) evaluating worker ${workerId} (Node: ${workerData.nodeId || 'N/A'})`, { </span>
        status: workerData.status, 
        capacity: workerData.capacity, 
        currentLoad: workerData.currentLoad 
      });
      
      const capacity = <span class="cstat-no" title="statement not covered" >workerData.capacity || 0;</span>
      const currentLoad = <span class="cstat-no" title="statement not covered" >workerData.currentLoad || 0;</span>
&nbsp;
      // Worker must be in a ready state (e.g., 'registered', 'active', or status not yet set but has capacity)
      // and have defined capacity.
<span class="cstat-no" title="statement not covered" >      if ((!workerData.status || workerData.status === 'registered' || workerData.status === 'active') &amp;&amp; capacity &gt; 0) {</span>
<span class="cstat-no" title="statement not covered" >        if (currentLoad &lt; capacity) {</span>
          // Calculate a score. Lower is better.
          // Prioritize workers with a lower load ratio (currentLoad / capacity).
          // As a tie-breaker, or secondary factor, prefer workers with more absolute available spots.
          const loadRatio = <span class="cstat-no" title="statement not covered" >currentLoad / capacity;</span>
          const availableSlots = <span class="cstat-no" title="statement not covered" >capacity - currentLoad;</span>
          
          // Example scoring: Primary factor is loadRatio, secondary is -(availableSlots) to favor more slots.
          // Multiplying availableSlots by a small negative number makes more slots better (smaller score).
          const currentScore = <span class="cstat-no" title="statement not covered" >loadRatio - (availableSlots * 0.001); </span>
&nbsp;
<span class="cstat-no" title="statement not covered" >          if (currentScore &lt; minLoadScore) {</span>
<span class="cstat-no" title="statement not covered" >            minLoadScore = currentScore;</span>
<span class="cstat-no" title="statement not covered" >            bestWorker = {id: workerId, ...workerData}; </span>// id is the map key (registration ID), workerData.nodeId is the worker's own reported ID
<span class="cstat-no" title="statement not covered" >            Logger.debug(`Master (${this.nodeId}) found new best worker candidate: ${workerId} (Node: ${bestWorker.nodeId}), Score: ${currentScore.toFixed(3)} (Load: ${currentLoad}/${capacity})`);</span>
          }
        } else {
<span class="cstat-no" title="statement not covered" >          Logger.debug(`Master (${this.nodeId}): Worker ${workerId} (Node: ${workerData.nodeId || 'N/A'}) is at full capacity (${currentLoad}/${capacity}).`);</span>
        }
      } else {
<span class="cstat-no" title="statement not covered" >        Logger.debug(`Master (${this.nodeId}): Worker ${workerId} (Node: ${workerData.nodeId || 'N/A'}) is not suitable (Status: ${workerData.status}, Capacity: ${capacity}).`);</span>
      }
    }
&nbsp;
<span class="cstat-no" title="statement not covered" >    if (bestWorker) {</span>
<span class="cstat-no" title="statement not covered" >      Logger.info(`Master (${this.nodeId}) selected worker ${bestWorker.id} (Node: ${bestWorker.nodeId || 'N/A'}) for new agent. Load: ${bestWorker.currentLoad}/${bestWorker.capacity}. Score: ${minLoadScore.toFixed(3)}`);</span>
    } else {
<span class="cstat-no" title="statement not covered" >      Logger.warn(`Master (${this.nodeId}) found no suitable worker for new agent assignment among ${this.workerNodes.size} workers.`);</span>
    }
<span class="cstat-no" title="statement not covered" >    return bestWorker;</span>
  }
&nbsp;
  // --- Worker-Side Command Handlers (called by Master via conceptual RPC) ---
<span class="fstat-no" title="function not covered" >  as</span>ync handleMasterCommand_CreateAgent(agentId, streamConfig = <span class="branch-0 cbranch-no" title="branch not covered" >null,</span> masterNodeIdInitiator) {
<span class="cstat-no" title="statement not covered" >    if (this.nodeRole !== 'worker') {</span>
<span class="cstat-no" title="statement not covered" >      Logger.error(`Node ${this.nodeId} (Role: ${this.nodeRole}) received CreateAgent command from ${masterNodeIdInitiator} but is not a worker. Ignoring.`);</span>
<span class="cstat-no" title="statement not covered" >      return { success: false, error: 'Not a worker node' };</span>
    }
<span class="cstat-no" title="statement not covered" >    Logger.info(`Worker node (${this.nodeId}) received command from Master (${masterNodeIdInitiator}) to create agent ${agentId}.`);</span>
<span class="cstat-no" title="statement not covered" >    try {</span>
<span class="cstat-no" title="statement not covered" >      await this._createAgentLocally(agentId, streamConfig);</span>
      // CONCEPTUAL CALLBACK/REPORT TO MASTER:
      // In a real system: await this.rpcCallToMaster('reportAgentCreated', { workerNodeId: this.nodeId, agentId, status: 'idle' });
      // Master's handleWorkerReport_AgentCreated would be invoked.
<span class="cstat-no" title="statement not covered" >      Logger.info(`Worker node (${this.nodeId}) successfully created agent ${agentId}. It would now report success to master (${masterNodeIdInitiator}).`);</span>
<span class="cstat-no" title="statement not covered" >      return { success: true, agentId };</span>
    } catch (error) {
<span class="cstat-no" title="statement not covered" >      Logger.error(`Worker node (${this.nodeId}) failed to create agent ${agentId} as commanded by master ${masterNodeIdInitiator}.`, { error: error.message });</span>
      // CONCEPTUAL CALLBACK/REPORT TO MASTER:
      // In a real system: await this.rpcCallToMaster('reportAgentCreationFailed', { workerNodeId: this.nodeId, agentId, error: error.message });
<span class="cstat-no" title="statement not covered" >      Logger.info(`Worker node (${this.nodeId}) failed to create agent ${agentId}. It would now report failure to master (${masterNodeIdInitiator}).`);</span>
<span class="cstat-no" title="statement not covered" >      return { success: false, error: error.message, agentId };</span>
    }
  }
&nbsp;
<span class="fstat-no" title="function not covered" >  as</span>ync handleMasterCommand_DestroyAgent(agentId, reason = <span class="branch-0 cbranch-no" title="branch not covered" >'master_command',</span> masterNodeIdInitiator) {
<span class="cstat-no" title="statement not covered" >    if (this.nodeRole !== 'worker') {</span>
<span class="cstat-no" title="statement not covered" >      Logger.error(`Node ${this.nodeId} (Role: ${this.nodeRole}) received DestroyAgent command from ${masterNodeIdInitiator} but is not a worker. Ignoring.`);</span>
<span class="cstat-no" title="statement not covered" >      return { success: false, error: 'Not a worker node' };</span>
    }
<span class="cstat-no" title="statement not covered" >    Logger.info(`Worker node (${this.nodeId}) received command from Master (${masterNodeIdInitiator}) to destroy agent ${agentId}. Reason: ${reason}`);</span>
<span class="cstat-no" title="statement not covered" >    try {</span>
<span class="cstat-no" title="statement not covered" >      await this._destroyAgentLocally(agentId, reason);</span>
      // CONCEPTUAL CALLBACK/REPORT TO MASTER:
      // In a real system: await this.rpcCallToMaster('reportAgentDestroyed', { workerNodeId: this.nodeId, agentId });
      // Master's handleWorkerReport_AgentDestroyed would be invoked.
<span class="cstat-no" title="statement not covered" >      Logger.info(`Worker node (${this.nodeId}) successfully destroyed agent ${agentId}. It would now report success to master (${masterNodeIdInitiator}).`);</span>
<span class="cstat-no" title="statement not covered" >      return { success: true, agentId };</span>
    } catch (error) {
<span class="cstat-no" title="statement not covered" >      Logger.error(`Worker node (${this.nodeId}) failed to destroy agent ${agentId} as commanded by master ${masterNodeIdInitiator}.`, { error: error.message });</span>
      // CONCEPTUAL CALLBACK/REPORT TO MASTER:
      // In a real system: await this.rpcCallToMaster('reportAgentDestructionFailed', { workerNodeId: this.nodeId, agentId, error: error.message });
<span class="cstat-no" title="statement not covered" >      Logger.info(`Worker node (${this.nodeId}) failed to destroy agent ${agentId}. It would now report failure to master (${masterNodeIdInitiator}).`);</span>
<span class="cstat-no" title="statement not covered" >      return { success: false, error: error.message, agentId };</span>
    }
  }
&nbsp;
  // --- Master-Side Worker Report Handlers ---
<span class="fstat-no" title="function not covered" >  as</span>ync handleWorkerReport_AgentCreated(workerNodeId, agentId, details = <span class="branch-0 cbranch-no" title="branch not covered" >{})</span> {
<span class="cstat-no" title="statement not covered" >    if (this.nodeRole !== 'master') {</span>
<span class="cstat-no" title="statement not covered" >      Logger.error(`Node ${this.nodeId} (Role: ${this.nodeRole}) received AgentCreated report but is not master. Ignoring.`);</span>
<span class="cstat-no" title="statement not covered" >      return;</span>
    }
    const workerRegId = <span class="cstat-no" title="statement not covered" >Object.keys(this.workerNodes).find(<span class="fstat-no" title="function not covered" >re</span>gId =&gt; <span class="cstat-no" title="statement not covered" >this.workerNodes[regId]?.nodeId === workerNodeId)</span>;</span>
<span class="cstat-no" title="statement not covered" >    if (!this.agentToWorkerMap.has(agentId) || this.agentToWorkerMap.get(agentId) !== workerRegId) {</span>
<span class="cstat-no" title="statement not covered" >        Logger.warn(`Master (${this.nodeId}) received AgentCreated report for agent ${agentId} from worker ${workerNodeId}, but this agent is not mapped or mapped to a different worker. Ignoring or re-evaluating mapping.`);</span>
        // Could be a late report or a mapping issue.
<span class="cstat-no" title="statement not covered" >        return;</span>
    }
&nbsp;
<span class="cstat-no" title="statement not covered" >    Logger.info(`Master (${this.nodeId}) received report: Agent ${agentId} created successfully on worker ${workerNodeId}. Details:`, details);</span>
<span class="cstat-no" title="statement not covered" >    try {</span>
        // Update master's placeholder record for this agent.
        // Assuming status from worker would be 'idle' or 'active_on_worker'
        const newStatus = <span class="cstat-no" title="statement not covered" >details.status || 'active_on_worker';</span>
<span class="cstat-no" title="statement not covered" >        await DatabaseManager.updateAgentStatus(agentId, newStatus, details.streamId || null, this.nodeId);</span>
<span class="cstat-no" title="statement not covered" >        await DatabaseManager.logAgentEvent(agentId, 'delegation_confirmed_created', {</span>
            masterNodeId: this.nodeId,
            workerNodeId,
            reportedStatus: newStatus
        });
<span class="cstat-no" title="statement not covered" >        Logger.info(`Master (${this.nodeId}) updated status for delegated agent ${agentId} to ${newStatus} based on worker report.`);</span>
    } catch (error) {
<span class="cstat-no" title="statement not covered" >        Logger.error(`Master (${this.nodeId}) failed to update DB for agent ${agentId} after creation report from worker ${workerNodeId}.`, { error: error.message });</span>
    }
  }
&nbsp;
<span class="fstat-no" title="function not covered" >  as</span>ync handleWorkerReport_AgentDestroyed(workerNodeId, agentId, details = <span class="branch-0 cbranch-no" title="branch not covered" >{})</span> {
<span class="cstat-no" title="statement not covered" >    if (this.nodeRole !== 'master') {</span>
<span class="cstat-no" title="statement not covered" >      Logger.error(`Node ${this.nodeId} (Role: ${this.nodeRole}) received AgentDestroyed report but is not master. Ignoring.`);</span>
<span class="cstat-no" title="statement not covered" >      return;</span>
    }
    const workerRegId = <span class="cstat-no" title="statement not covered" >Object.keys(this.workerNodes).find(<span class="fstat-no" title="function not covered" >re</span>gId =&gt; <span class="cstat-no" title="statement not covered" >this.workerNodes[regId]?.nodeId === workerNodeId)</span>;</span>
    // Check if agent was mapped to this worker. It should have been removed from map when master initiated destruction command.
    // However, worker might report destruction for other reasons (e.g. local error on worker).
<span class="cstat-no" title="statement not covered" >    Logger.info(`Master (${this.nodeId}) received report: Agent ${agentId} destroyed on worker ${workerNodeId}. Details:`, details);</span>
<span class="cstat-no" title="statement not covered" >    try {</span>
        // Update master's placeholder record for this agent.
<span class="cstat-no" title="statement not covered" >        await DatabaseManager.updateAgentStatus(agentId, 'terminated_on_worker', null, this.nodeId);</span>
<span class="cstat-no" title="statement not covered" >        await DatabaseManager.logAgentEvent(agentId, 'delegation_confirmed_destroyed', {</span>
            masterNodeId: this.nodeId,
            workerNodeId,
            reason: details.reason || 'worker_reported_destruction'
        });
<span class="cstat-no" title="statement not covered" >        Logger.info(`Master (${this.nodeId}) marked delegated agent ${agentId} as terminated_on_worker based on report from ${workerNodeId}.`);</span>
        // Ensure it's removed from agentToWorkerMap if it wasn't already (e.g. worker-initiated destruction)
<span class="cstat-no" title="statement not covered" >        if (this.agentToWorkerMap.has(agentId) &amp;&amp; this.agentToWorkerMap.get(agentId) === workerRegId) {</span>
<span class="cstat-no" title="statement not covered" >            this.agentToWorkerMap.delete(agentId);</span>
<span class="cstat-no" title="statement not covered" >            Logger.info(`Master (${this.nodeId}) removed agent ${agentId} from agentToWorkerMap after destruction report from worker ${workerNodeId}.`);</span>
            // Adjust worker load if master hadn't already accounted for it
            const workerData = <span class="cstat-no" title="statement not covered" >this.workerNodes.get(workerRegId);</span>
<span class="cstat-no" title="statement not covered" >            if (workerData &amp;&amp; workerData.currentLoad &gt; 0) {</span>
<span class="cstat-no" title="statement not covered" >                workerData.currentLoad--;</span>
<span class="cstat-no" title="statement not covered" >                this.workerNodes.set(workerRegId, workerData);</span>
<span class="cstat-no" title="statement not covered" >                Logger.debug(`Master (${this.nodeId}) adjusted load for worker ${workerRegId} to ${workerData.currentLoad}/${workerData.capacity} after destruction report.`);</span>
            }
        }
    } catch (error) {
<span class="cstat-no" title="statement not covered" >        Logger.error(`Master (${this.nodeId}) failed to update DB for agent ${agentId} after destruction report from worker ${workerNodeId}.`, { error: error.message });</span>
    }
  }
}
&nbsp;
<span class="cstat-no" title="statement not covered" >module.exports = new FleetController(); </span></pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-06-04T07:23:52.691Z
            </div>
        <script src="../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../sorter.js"></script>
        <script src="../block-navigation.js"></script>
    </body>
</html>
    