
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for distributed</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../prettify.css" />
    <link rel="stylesheet" href="../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../index.html">All files</a> distributed</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">24.56% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>85/346</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">27.77% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>40/144</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">26.76% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>19/71</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">26.08% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>84/322</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file low" data-value="DeploymentManager.js"><a href="DeploymentManager.js.html">DeploymentManager.js</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="199" class="abs low">0/199</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="76" class="abs low">0/76</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="38" class="abs low">0/38</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="185" class="abs low">0/185</td>
	</tr>

<tr>
	<td class="file high" data-value="ResourceOptimizer.js"><a href="ResourceOptimizer.js.html">ResourceOptimizer.js</a></td>
	<td data-value="92.15" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 92%"></div><div class="cover-empty" style="width: 8%"></div></div>
	</td>
	<td data-value="92.15" class="pct high">92.15%</td>
	<td data-value="51" class="abs high">47/51</td>
	<td data-value="78.04" class="pct medium">78.04%</td>
	<td data-value="41" class="abs medium">32/41</td>
	<td data-value="90" class="pct high">90%</td>
	<td data-value="10" class="abs high">9/10</td>
	<td data-value="95.91" class="pct high">95.91%</td>
	<td data-value="49" class="abs high">47/49</td>
	</tr>

<tr>
	<td class="file low" data-value="SharedStateManager.js"><a href="SharedStateManager.js.html">SharedStateManager.js</a></td>
	<td data-value="39.58" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 39%"></div><div class="cover-empty" style="width: 61%"></div></div>
	</td>
	<td data-value="39.58" class="pct low">39.58%</td>
	<td data-value="96" class="abs low">38/96</td>
	<td data-value="29.62" class="pct low">29.62%</td>
	<td data-value="27" class="abs low">8/27</td>
	<td data-value="43.47" class="pct low">43.47%</td>
	<td data-value="23" class="abs low">10/23</td>
	<td data-value="42.04" class="pct low">42.04%</td>
	<td data-value="88" class="abs low">37/88</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-06-04T07:23:52.626Z
            </div>
        <script src="../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../sorter.js"></script>
        <script src="../block-navigation.js"></script>
    </body>
</html>
    