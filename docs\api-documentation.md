# API Documentation for Twitch RPG Viewer Fleet Management System

This document outlines the API specifications for the various components within the Twitch RPG Viewer Fleet Management System.

## 1. Fleet Controller API

The Fleet Controller is the central orchestration unit. In a distributed setup, the Master node exposes an API for Worker nodes to register, send heartbeats, and report status. It may also provide endpoints for the Dashboard.

### 1.1. Worker-Facing Endpoints (Master Node)

These endpoints are exposed by the Master FleetController for Worker nodes.

#### `POST /worker/register`
- **Purpose:** Allows a new Worker node to register with the Master.
- **Request Body:**
  ```json
  {
    "workerId": "unique-worker-node-id",
    "address": "http://worker-ip-address",
    "port": 12346, // Port the worker listens on for commands from master
    "capacity": 50, // Max agents this worker can handle
    "nodeId": "worker-node-identifier-from-config", // Actual node ID from worker's config
    "currentResourceUsage": { // Optional: initial resource snapshot
      "cpu": "10%",
      "memory": "2GB/8GB"
    }
  }
  ```
- **Response Body (Success 200 OK):**
  ```json
  {
    "status": "registered",
    "masterNodeId": "master-node-identifier",
    "message": "Worker registered successfully."
  }
  ```
- **Response Body (Error 4xx/5xx):**
  ```json
  {
    "error": "Failed to register worker",
    "details": "Specific error message"
  }
  ```

#### `POST /worker/heartbeat`
- **Purpose:** Allows a Worker node to send its current status and load to the Master.
- **Request Body:**
  ```json
  {
    "workerId": "unique-worker-node-id",
    "currentLoad": 25, // Number of active agents on the worker
    "status": "active|maintenance|error",
    "agents": [ // Brief status of agents on this worker
      { "id": "agent-1", "status": "watching", "streamId": "stream123" },
      { "id": "agent-2", "status": "idle" }
    ],
    "resourceUsage": { // Current resource usage
        "cpu": "45%",
        "memory": "5GB/8GB",
        "diskUsage": "60%",
        "networkIO": "5Mbps"
    }
  }
  ```
- **Response Body (Success 200 OK):**
  ```json
  {
    "status": "heartbeat_received",
    "nextExpectedIn": 60 // seconds
  }
  ```

#### `POST /worker/report/agent-created`
- **Purpose:** Worker reports to Master that an agent was successfully created.
- **Request Body:**
  ```json
  {
    "workerNodeId": "unique-worker-node-id",
    "agentId": "newly-created-agent-id",
    "status": "idle_on_worker", // Or other initial status
    "details": { /* any other relevant creation details */ }
  }
  ```
- **Response Body (Success 200 OK):**
  ```json
  {
    "status": "report_acknowledged",
    "agentId": "newly-created-agent-id"
  }
  ```

#### `POST /worker/report/agent-destroyed`
- **Purpose:** Worker reports to Master that an agent was destroyed.
- **Request Body:**
  ```json
  {
    "workerNodeId": "unique-worker-node-id",
    "agentId": "destroyed-agent-id",
    "reason": "master_command|local_error|resource_reallocation",
    "details": { /* any other relevant destruction details */ }
  }
  ```
- **Response Body (Success 200 OK):**
  ```json
  {
    "status": "report_acknowledged",
    "agentId": "destroyed-agent-id"
  }
  ```

### 1.2. Master-Facing Endpoints (Worker Node)

These endpoints are exposed by Worker nodes for the Master FleetController to send commands.

#### `POST /command/create-agent`
- **Purpose:** Master commands Worker to create a new agent.
- **Request Body:**
  ```json
  {
    "agentId": "master-assigned-agent-id",
    "masterNodeIdInitiator": "master-node-id",
    "streamConfig": { /* Optional: initial stream or persona config */ }
  }
  ```
- **Response Body (Success 200 OK - Accepted for processing):**
  ```json
  {
    "status": "command_received",
    "agentId": "master-assigned-agent-id",
    "message": "Agent creation process initiated."
  }
  ```

#### `POST /command/destroy-agent`
- **Purpose:** Master commands Worker to destroy an existing agent.
- **Request Body:**
  ```json
  {
    "agentId": "agent-id-to-destroy",
    "masterNodeIdInitiator": "master-node-id",
    "reason": "scale_down|maintenance"
  }
  ```
- **Response Body (Success 200 OK - Accepted for processing):**
  ```json
  {
    "status": "command_received",
    "agentId": "agent-id-to-destroy",
    "message": "Agent destruction process initiated."
  }
  ```

#### `POST /command/global-broadcast`
- **Purpose:** Master sends a global command to be processed by the worker (e.g., pause activity, update config).
- **Request Body:**
  ```json
  {
    "commandType": "PAUSE_ALL_ACTIVITY|UPDATE_TARGET_GAMES|REFRESH_CONFIG",
    "payload": { /* command-specific data */ },
    "masterNodeIdInitiator": "master-node-id"
  }
  ```
- **Response Body (Success 200 OK - Accepted for processing):**
  ```json
  {
    "status": "command_acknowledged",
    "commandType": "PAUSE_ALL_ACTIVITY",
    "message": "Global command received and is being processed."
  }
  ```

### 1.3. Dashboard-Facing Endpoints (Master Node or Standalone)

Endpoints for the Dashboard to interact with the FleetController.

#### `GET /fleet/status`
- **Purpose:** Get overall fleet status.
- **Response Body:**
  ```json
  {
    "totalAgents": 100,
    "activeAgents": 80,
    "idleAgents": 15,
    "errorAgents": 5,
    "currentScaleLevel": "medium",
    "targetUtilization": 0.8,
    "actualUtilization": 0.75,
    "workerNodesCount": 3, // If distributed
    "workerNodeDetails": [ // If distributed
      { "id": "worker-1", "load": 25, "capacity": 30, "status": "active", "resources": {"cpu": "50%", "memory": "4GB/8GB"} }
    ]
  }
  ```

#### `GET /fleet/agents`
- **Purpose:** Get a list of all agents and their detailed status.
- **Query Params:** `?status=active&limit=50&offset=0&sortBy=lastActivity&order=desc`
- **Response Body:**
  ```json
  {
    "agents": [
      {
        "id": "agent-123",
        "status": "watching",
        "streamId": "twitchstream456",
        "game": "World of Warcraft",
        "assignedWorkerId": "worker-1", // if applicable
        "nodeId": "worker-node-A", // Actual node running the agent
        "lastActivity": "2024-07-30T10:00:00Z",
        "metrics": { "messagesSent": 10, "errors": 0 }
      }
    ],
    "totalCount": 100,
    "limit": 50,
    "offset": 0
  }
  ```

#### `POST /fleet/scale`
- **Purpose:** Manually trigger a scale up/down or adjust target agent count (admin action).
- **Request Body:**
  ```json
  {
    "action": "set_target_agents|change_scale_level",
    "value": 120, // or "large"
    "levelName": "new_level_name" // if action is change_scale_level
  }
  ```
- **Response Body:**
  ```json
  {
    "status": "scaling_initiated|level_change_initiated",
    "message": "Fleet adjustment process started."
  }
  ```

#### `POST /agent/:agentId/command`
- **Purpose:** Send a command to a specific agent (e.g., restart, change stream).
- **Request Body:**
  ```json
  {
    "command": "restart|change_stream|pause",
    "payload": { "newStreamId": "anotherstream789" } // if applicable
  }
  ```
- **Response Body:**
  ```json
  {
    "status": "command_sent",
    "agentId": "agent-123",
    "message": "Command dispatched to agent."
  }
  ```

## 2. AgentManager API (Internal Interface)

The AgentManager's public methods define its internal API used by the FleetController.

- `async createAgent(agentId, config)`: Creates and initializes a new agent instance.
- `async destroyAgent(agentId)`: Stops and cleans up an agent instance.
- `async getAgentStatus(agentId)`: Returns the current status of a specific agent.
- `async updateAgentConfig(agentId, newConfig)`: Updates the configuration for a running agent.
- `getAllAgents()`: Returns a list of all agents managed by this manager instance and their status.

## 3. RPGStreamManager API (Internal Interface)

Provides stream data to the FleetController and Agents.

- `async getPrioritizedRpgStreams(count)`: Returns a list of RPG streams, prioritized by configuration.
- `async getStreamDetails(streamId)`: Returns detailed information for a specific stream.
- `isStreamRpg(streamId)`: Checks if a stream is still in the RPG category.
- `getGameMetadata(gameNameOrId)`: Returns metadata for a specific game.

## 4. GeminiClient API (Internal Interface)

Wrapper for interacting with the Google Gemini API.

- `async analyzeStreamContent(multimodalInput)`: Sends screenshot, audio, chat history to Gemini for analysis.
  - `multimodalInput`: `{ screenshot: Buffer, audioTranscript: string, chatHistory: Array, gameInfo: Object, promptTemplate: string }`
- `async generateChatMessage(analysisContext, personaConfig)`: Generates a chat message based on Gemini's analysis.

## 5. DashboardServer API

The DashboardServer (e.g., Express.js) exposes endpoints for the React UI. These typically mirror the Dashboard-Facing FleetController API but might involve additional data transformation or aggregation.

#### `GET /api/dashboard/overview`
- **Purpose:** Provides aggregated data for the main dashboard view.
- **Response Body:** Similar to `/fleet/status` but potentially with more UI-specific formatting or historical data points.

#### `GET /api/dashboard/agents`
- **Purpose:** Fetches agent list with filtering/sorting for the agent table.
- **Query Params:** `?filter_status=active&sort_by=uptime&page=1&limit=20`
- **Response Body:** Similar to `/fleet/agents`.

#### `GET /api/dashboard/agent/:id/details`
- **Purpose:** Get detailed logs or extended status for a single agent.
- **Response Body:**
  ```json
  {
    "agentId": "agent-123",
    "status": "watching",
    "logs": [
      { "timestamp": "...", "level": "info", "message": "Joined stream X" },
      { "timestamp": "...", "level": "debug", "message": "Sent chat: Hello!" }
    ],
    "performanceMetrics": { /* detailed metrics */ }
  }
  ```

#### `POST /api/dashboard/agent/:id/action`
- **Purpose:** Relays actions from the dashboard UI to the FleetController for a specific agent.
- **Request Body:**
  ```json
  {
    "action": "restart|terminate|view_browser_window"
  }
  ```
- **Response Body:**
  ```json
  {
    "status": "action_initiated",
    "message": "Action sent to agent."
  }
  ```

This API documentation provides a high-level overview. Detailed request/response schemas, authentication mechanisms, and error codes would be further defined for each endpoint during implementation. 