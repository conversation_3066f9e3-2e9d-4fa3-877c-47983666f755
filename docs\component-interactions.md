# Component Interaction Guides for Twitch RPG Viewer Fleet Management System

This document describes the interactions between the major components of the Twitch RPG Viewer Fleet Management System during key operational scenarios.

## Core Components Involved:

- **FleetController (FC)**: Central orchestrator. In distributed mode, differentiates between Master FC and Worker FC.
- **AgentManager (AM)**: Manages lifecycle of agent instances on a specific node.
- **Agent Instance (Agent)**: A single browser-based viewer.
- **RPGStreamManager (RSM)**: Discovers and provides information about RPG streams.
- **GeminiClient (GC)**: Handles communication with the Google Gemini API.
- **DatabaseManager (DBM)**: Interface for database operations.
- **DashboardServer (DS)**: Backend for the monitoring UI.
- **ConfigManager (CM)**: Provides configuration values to all components.

## Scenario 1: System Startup and Agent Initialization (Standalone Mode)

1.  **Application Start**: Main process starts.
2.  **ConfigManager (CM) Initialization**: Loads configuration from `.env` and defaults.
3.  **FleetController (FC) Initialization**:
    *   Reads configuration from CM (maxAgents, minAgents, etc.).
    *   Initializes `DatabaseManager (DBM)`.
    *   Calls `FC.loadExistingAgents()`: DBM fetches any persisted agent states.
    *   Calls `FC.startHealthMonitoring()`: Sets up periodic health checks.
    *   If `minAgents` > 0, `FC.performHealthCheck()` eventually calls `FC.adjustFleetSize()`, which calls `FC.scaleUp()`.
4.  **FC.scaleUp()**:
    *   Determines number of agents to add (up to `scaleUpBatchSize` and `maxAgents`).
    *   For each new agent, calls `FC.createAgent()`.
5.  **FC.createAgent()** (Standalone/Local Creation Logic):
    *   Generates a unique `agentId`.
    *   Calls `FC._createAgentLocally(agentId)`.
6.  **FC._createAgentLocally(agentId)**:
    *   Calls `DBM.createAgent(agentId, this.nodeId, { status: 'idle' })` to record the agent in the database.
    *   Creates an entry in its local `this.agents` map.
    *   Logs event via `DBM.logAgentEvent(agentId, 'created_locally', ...)`.
    *   *(Conceptual: This is where it would interact with an `AgentManager` if `AgentManager` was a separate class directly instantiated and controlled by `FleetController` for local agents. In the current `FleetController` structure, the `FC` itself handles aspects of local agent state).* The actual browser instance creation and management would be delegated to an `AgentManager`-like entity or directly handled if simpler for Electron.
7.  **Agent Instance Lifecycle (Conceptual via AgentManager/FC interaction)**:
    *   The (conceptual) `AgentManager` would be instructed to launch a browser for `agentId`.
    *   Agent authenticates to Twitch, joins an initial idle state.
    *   Agent reports its status back through FC, which updates DBM.

## Scenario 2: Stream Selection and Agent Assignment (Standalone Mode)

1.  **FC Health Check / Agent Idle**: `FC.performHealthCheck()` runs. An agent might be found in `idle` state.
2.  **(Conceptual) Agent Needs Task**: FC determines an idle agent needs a stream.
3.  **RPGStreamManager (RSM) Invocation**: FC requests a prioritized list of RPG streams from RSM (`RSM.getPrioritizedRpgStreams()`).
4.  **RSM Logic**:
    *   Queries `TwitchApiClient` for live RPG streams.
    *   Filters based on `rpg_games.json` and `rpg_streamers.json` priorities.
    *   Returns a list of suitable streams to FC.
5.  **FC Assigns Stream**: FC selects a stream for the idle agent.
6.  **FC Updates Agent**: FC calls `FC.updateAgentStatus(agentId, 'assigning_stream', streamId)`.
7.  **(Conceptual) Agent Action**: The agent instance (via AM) is commanded to navigate to the assigned `streamId`.
8.  **Agent Reports Status**: Agent reports `watching` status. FC calls `FC.updateAgentStatus(agentId, 'watching', streamId)`.

## Scenario 3: Content Analysis and Message Generation Cycle

1.  **Agent Active**: Agent is watching a stream.
2.  **Content Capture**: Agent periodically captures:
    *   Screenshot of the stream.
    *   Audio snippet (transcribed locally or by GC).
    *   Recent chat messages from the stream.
3.  **GeminiClient (GC) Invocation**: Agent (or AM on its behalf) prepares `multimodalInput`.
    *   `gameInfo` is fetched from RSM (`RSM.getGameMetadata()`).
    *   Appropriate prompt template is selected (e.g., from `gemini_prompt_templates.md`).
    *   Calls `GC.analyzeStreamContent(multimodalInput)`.
4.  **GC to Gemini API**: GC sends the request to Google Gemini API, potentially including `thinkingConfig: { includeThoughts: true }`.
5.  **Gemini API Processes**: Gemini analyzes content and returns analysis and suggested message (and thoughts if requested).
6.  **GC Returns Response**: GC sends structured response back to the agent/AM.
7.  **Message Decision & Sending**: Agent (or AM):
    *   Evaluates the `suggestedMessage` and `confidenceScore`.
    *   Considers its persona and message timing/rate limits (`ConfigManager`).
    *   If criteria met, sends the message to Twitch chat via its browser instance.
    *   Logs message event via `DBM.logAgentEvent(agentId, 'message_sent', ...)`.

## Scenario 4: Agent Error and Recovery (Standalone Mode)

1.  **Error Occurs**: An agent instance encounters an error (e.g., browser crash, Twitch disconnect).
2.  **Error Detection**: FC's `performHealthCheck()` identifies an agent in an error state, or an agent self-reports an error.
3.  **FC.handleErroredAgent()**:
    *   Increments error count for the agent (`this.errorCounts`).
    *   If `errorCount >= maxRetries (from CM)`:
        *   Calls `FC.destroyAgent(agentId)`.
        *   `FC.destroyAgent()` calls `FC._destroyAgentLocally()` which updates DBM (`terminated`) and removes from `this.agents`.
        *   The health check will later trigger `FC.adjustFleetSize()` to create a new agent if below `minAgents`.
    *   Else (errorCount < maxRetries):
        *   Calls `FC.retryAgent(agentId)`.
4.  **FC.retryAgent()**:
    *   Logs retry attempt via `DBM.logAgentEvent()`.
    *   Updates agent status to `idle` via `FC.updateAgentStatus()` (which updates DBM).
    *   Agent will be picked up for a new task in a subsequent cycle.

## Scenario 5: Master-Worker Communication (Distributed Mode)

### 5.1 Worker Registration:

1.  **Worker FC Starts**: `Worker FC.initialize()` is called.
2.  `Worker FC.initializeWorkerNode()`:
    *   Prepares `registrationDetails` (workerId, address, port, capacity, nodeId from CM).
    *   *(Conceptual RPC)* Sends registration request to `Master FC URL (from CM)` at `/worker/register` endpoint.
3.  **Master FC Receives Registration**: `Master FC.handleWorkerRegistration(workerId, workerInfo)` is invoked (e.g., via an HTTP request handler).
    *   Validates workerInfo.
    *   Adds/updates worker in `Master FC.workerNodes` map with `currentLoad: 0`, `lastSeen`, `status: 'registered'`.
    *   Responds with success to Worker FC.

### 5.2 Worker Heartbeat:

1.  **Worker FC Timer**: Periodically, Worker FC gathers its status.
2.  `Worker FC` prepares `heartbeatData` (currentLoad, status, agent details, resourceUsage from CM/OS).
3.  *(Conceptual RPC)* Sends heartbeat to `Master FC URL` at `/worker/heartbeat` endpoint.
4.  **Master FC Receives Heartbeat**: `Master FC.handleWorkerHeartbeat(workerId, heartbeatData)` is invoked.
    *   Validates `workerId`.
    *   Updates `workerData` in `Master FC.workerNodes` (load, lastSeen, status, resource metrics).

### 5.3 Agent Creation Delegated to Worker:

1.  **Master FC Decides to Scale**: `Master FC.scaleUp()` calls `Master FC.createAgent()`.
2.  `Master FC.createAgent()`:
    *   Calls `Master FC.selectWorkerForNewAgent()` to choose a worker (e.g., `selectedWorker`).
    *   If `selectedWorker` found:
        *   Generates `agentId`.
        *   Stores `agentId -> selectedWorker.id` in `Master FC.agentToWorkerMap`.
        *   Calls `DBM.createAgent(agentId, masterNodeId, { status: 'pending_delegation', assigned_worker_id: selectedWorker.id })`.
        *   *(Conceptual RPC)* Master FC sends command to `selectedWorker.address` at `/command/create-agent` with `{ agentId, masterNodeIdInitiator }`.
        *   Optimistically updates `selectedWorker.currentLoad` in `Master FC.workerNodes`.
3.  **Worker FC Receives Command**: `Worker FC.handleMasterCommand_CreateAgent(agentId, streamConfig, masterNodeIdInitiator)` is invoked.
    *   Calls `Worker FC._createAgentLocally(agentId, streamConfig)`.
        *   `DBM.createAgent(agentId, workerNodeId, { status: 'idle' })` (worker's DBM or central DBM).
        *   Adds to `Worker FC.agents` map.
    *   *(Conceptual RPC)* Worker FC reports success to Master via `/worker/report/agent-created` with `{ workerNodeId, agentId, status: 'idle_on_worker' }`.
4.  **Master FC Receives Report**: `Master FC.handleWorkerReport_AgentCreated(workerNodeId, agentId, details)`.
    *   Updates DBM status for `agentId` from `pending_delegation` to `details.status` (e.g., `active_on_worker`).
    *   Confirms/adjusts worker load if necessary.

### 5.4 Agent Destruction Delegated to Worker:

1.  **Master FC Decides to Scale Down**: `Master FC.scaleDown()` identifies `agentId` to destroy.
2.  `Master FC.destroyAgent(agentId, reason)`:
    *   Looks up `workerId` from `Master FC.agentToWorkerMap` for `agentId`.
    *   If `workerId` found:
        *   Updates DBM status for `agentId` to `terminating_delegated`.
        *   *(Conceptual RPC)* Master FC sends command to `worker.address` at `/command/destroy-agent` with `{ agentId, reason, masterNodeIdInitiator }`.
        *   Removes `agentId` from `Master FC.agentToWorkerMap`.
        *   Decrements `worker.currentLoad` in `Master FC.workerNodes`.
3.  **Worker FC Receives Command**: `Worker FC.handleMasterCommand_DestroyAgent(agentId, reason, masterNodeIdInitiator)`.
    *   Calls `Worker FC._destroyAgentLocally(agentId, reason)`.
        *   Updates DBM status to `terminated`.
        *   Removes from `Worker FC.agents` map.
    *   *(Conceptual RPC)* Worker FC reports success to Master via `/worker/report/agent-destroyed` with `{ workerNodeId, agentId, reason }`.
4.  **Master FC Receives Report**: `Master FC.handleWorkerReport_AgentDestroyed(workerNodeId, agentId, details)`.
    *   Updates DBM status for `agentId` to `terminated_on_worker`.
    *   Ensures `agentToWorkerMap` is clear for this `agentId` and worker load is accurate.

## Scenario 6: Dashboard Data Flow

1.  **Dashboard UI Loads**: React app starts.
2.  **API Call for Overview**: UI makes a request to `DashboardServer (DS)` at `/api/dashboard/overview`.
3.  **DS Fetches Data**: `DS` (e.g., Express route handler):
    *   If standalone: Directly calls appropriate methods on the single `FleetController` instance (e.g., get overall status, agent counts).
    *   If distributed (DS talks to Master FC): Makes an HTTP request to Master FC's `/fleet/status` endpoint (defined in `api-documentation.md`).
4.  **FC Responds**: `FleetController` gathers data:
    *   Counts agents in `this.agents` (local) or summarizes from `this.workerNodes` and `this.agentToWorkerMap` (master).
    *   Retrieves data from DBM if needed.
    *   Returns JSON response.
5.  **DS Relays Data**: `DS` forwards the JSON response to the Dashboard UI.
6.  **UI Renders**: Dashboard UI displays the overview data.
7.  **User Action (e.g., View Agent Details)**:
    *   User clicks on an agent in the UI.
    *   UI makes a request to `DS` at `/api/dashboard/agent/:id/details`.
    *   `DS` fetches detailed data for that agent, potentially from FC or DBM.
    *   Data is returned and displayed.
8.  **User Command (e.g., Restart Agent)**:
    *   User clicks 'Restart' button for an agent.
    *   UI sends `POST` to `DS` at `/api/dashboard/agent/:id/action` with `{ action: 'restart' }`.
    *   `DS` relays this command to the `FleetController`.
        *   If standalone, FC handles it directly for the local agent.
        *   If distributed, Master FC identifies which worker hosts the agent and sends a command to that worker (e.g., `/command/destroy-agent` then `/command/create-agent`, or a specific `/command/restart-agent` if implemented).
    *   FC processes the command and DS returns acknowledgement to UI.

These scenarios illustrate the primary interactions. Actual implementation will involve more detailed error handling, security considerations (e.g., API keys for master-worker communication), and specific data formats for RPC/HTTP calls. 