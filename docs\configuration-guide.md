# Configuration Guide for Twitch RPG Viewer Fleet Management System

This guide explains how to configure the Twitch RPG Viewer Fleet Management System for various operational scenarios.

## 1. Core Configuration File: `.env`

The primary method for configuring the application is through an `.env` file located in the root of the project. This file stores environment-specific variables.

- **Creating `.env`**: Copy the example file `tasks/env.example` to `.env` in the project root: `cp tasks/env.example .env`
- **Editing `.env`**: Open the `.env` file in a text editor and modify the variables according to your setup.

**Important**: The `.env` file should **never** be committed to version control if it contains sensitive information like API keys.

## 2. Key Configuration Sections and Variables

Refer to `tasks/env.example` for a full list of variables. Below are explanations for critical sections.

### 2.1. API Keys

These are essential for the application to function.

- `TWITCH_CLIENT_ID`: Your Twitch application's Client ID.
- `TWITCH_CLIENT_SECRET`: Your Twitch application's Client Secret.
- `GEMINI_API_KEY`: Your Google Gemini API key for content analysis and message generation.

**Security**: Ensure these keys are kept confidential.

### 2.2. Paths

These define where the application looks for important files.

- `DATABASE_PATH`: Path to the SQLite database file (e.g., `./db/fleet-manager.db`).
- `ACCOUNTS_PATH`: Path to the JSON file containing Twitch account credentials for the agents (e.g., `./config/accounts.json`).
- `STREAMERS_PATH`: Path to the JSON file listing priority RPG streamers (e.g., `./config/rpg_streamers.json`).
- `LOG_PATH`: Directory where log files will be stored (e.g., `./logs`).

### 2.3. Core Operation Settings

These settings control the general behavior of the fleet.

- `MAX_AGENTS`: (Global Default) The maximum number of agents the system will attempt to run *if not overridden by a scale level*. (e.g., `100`).
- `AGENT_LAUNCH_INTERVAL`: Milliseconds between launching individual agents to avoid rate limiting (e.g., `5000`).
- `DASHBOARD_PORT`: Port for the monitoring dashboard web server (e.g., `3000`).
- `MESSAGE_MIN_INTERVAL`: Minimum milliseconds between messages from a single agent (e.g., `300000` for 5 minutes).
- `NODE_ENV`: Set to `production` for deployments, or `development` for debugging features.
- `DEBUG`: Set to `true` for verbose logging (boolean: `true` or `false`).

### 2.4. Distributed Deployment Settings

These settings are crucial if you plan to run the fleet across multiple machines.

- `DISTRIBUTED_ENABLED`: Set to `true` to enable distributed mode. Set to `false` for standalone operation on a single machine.
  - Example: `DISTRIBUTED_ENABLED=true`

- `NODE_ROLE`: Defines the role of the current instance. Can be `master` or `worker`.
  - `master`: This instance will act as the central controller, managing worker nodes and orchestrating the fleet.
  - `worker`: This instance will manage a subset of agents as directed by the master node.
  - Example: `NODE_ROLE=master` or `NODE_ROLE=worker`

- `MASTER_NODE_URL`: **Required for worker nodes.** The full URL (including port) of the master node that worker nodes should register with.
  - Example for a worker: `MASTER_NODE_URL=http://*************:12345`
  - For a master node, this URL defines how it expects workers to reach it (often its own IP and `NODE_PORT`).

- `NODE_PORT`: The port number this instance will use for its operations.
  - If `NODE_ROLE=master`, this is the port the master listens on for worker registrations and commands.
  - If `NODE_ROLE=worker`, this port might be used for the master to send direct commands to the worker (if the communication model requires it) or for worker-to-worker communication if implemented.
  - Example: `NODE_PORT=12345` (for master), `NODE_PORT=12346` (for a worker, if it needs to listen).

- `NODE_ID`: A unique identifier for this specific node instance. This helps in distinguishing logs and identifying nodes in a distributed setup.
  - For masters, can be a simple identifier like `master-main`.
  - For workers, it's good practice to make it unique, e.g., `worker-alpha-01`.
  - Example: `NODE_ID=master-node-primary` or `NODE_ID=worker-node-7b3f`

**Setup Example (Master):**
```env
DISTRIBUTED_ENABLED=true
NODE_ROLE=master
MASTER_NODE_URL=http://<master_ip_address>:12345 # Master's own reachable URL
NODE_PORT=12345
NODE_ID=master-01
```

**Setup Example (Worker):**
```env
DISTRIBUTED_ENABLED=true
NODE_ROLE=worker
MASTER_NODE_URL=http://<master_ip_address>:12345 # URL of the actual master node
# NODE_PORT for worker might not be strictly needed if communication is worker -> master only for reporting
# but can be used if master needs to directly ping worker, or for worker-specific services.
NODE_PORT=12346 
NODE_ID=worker-spot-instance-01
MAX_AGENTS=50 # Worker's local capacity
```

### 2.5. Scale Level Configuration

This allows defining pre-set configurations for different operational scales (e.g., "small", "medium", "large"). The FleetController uses these to adjust its behavior.

- `SCALE_LEVELS_CONFIG`: A JSON string defining various scale levels and their parameters. Parameters within a specific level (e.g., `maxAgents`, `minAgents`) will **override** the global default values if specified for that level.
  - **Structure**: The JSON string should be an object where each key is a scale level name (e.g., "small"), and the value is an object containing parameters for that level.
  - **Example (from `tasks/env.example`):**
    ```json
    {
      "small": {
        "maxAgents": 20,
        "minAgents": 5,
        "targetUtilization": 0.7,
        "scaleUpBatchSize": 2,
        "scaleDownBatchSize": 1
      },
      "medium": {
        "maxAgents": 100,
        "minAgents": 70
        // Other params like targetUtilization will use global defaults if not specified here
      }
    }
    ```
  - **In `.env` file (ensure it's a valid JSON string, often on a single line or carefully formatted if multiline is supported by your `.env` parser):**
    `SCALE_LEVELS_CONFIG='{"small":{"maxAgents":20,"minAgents":5,"targetUtilization":0.7},"medium":{"maxAgents":100,"minAgents":70}}'`

- `INITIAL_SCALE_LEVEL`: The name of the scale level (which must be a key in `SCALE_LEVELS_CONFIG`) that the FleetController should use when it starts.
  - Example: `INITIAL_SCALE_LEVEL=medium`

**How it works:**
1. The `ConfigManager` loads the `SCALE_LEVELS_CONFIG` JSON string and parses it.
2. It then reads the `INITIAL_SCALE_LEVEL`.
3. The `FleetController` (and potentially other components) will fetch its operational parameters (like `maxAgents`, `minAgents`, `targetUtilization`, `scaleUpBatchSize`, `scaleDownBatchSize`) by:
    a. First, checking if the parameter is defined in the `activeScaleConfig` (determined by `INITIAL_SCALE_LEVEL`).
    b. If not found in the active scale level, it falls back to the global default value for that parameter (which might have been set by another `.env` variable like `MAX_AGENTS` or an internal application default).

**Customization:**
- You can define as many scale levels as you need in `SCALE_LEVELS_CONFIG`.
- If a parameter is not specified for a particular scale level, the system will use its global default.
- This allows for fine-grained control. For example, a "low_activity_testing" scale level might have very low `minAgents` and `maxAgents`.

## 3. Dynamic Configuration (Future Considerations)

While the primary configuration is via `.env` at startup, future enhancements might include:
- Reloading configuration without restarting the entire application (e.g., via a dashboard command that triggers `ConfigManager.loadGlobalConfigFromEnvAndDefaults()` or specific reload methods).
- Storing some non-sensitive, dynamically changeable configurations in the database.

Always ensure that changes to `.env` are carefully managed, especially in a distributed environment, to maintain consistency across nodes or to ensure roles are correctly assigned upon restart. 