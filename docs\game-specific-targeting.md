# Game-Specific Targeting Strategies Guide

This guide explains how the Twitch RPG Viewer Fleet Management System uses configuration files and contextual information to target specific RPG games and streamers, and how to update these strategies.

## 1. Core Configuration Files for Targeting

Two primary JSON files drive the game-specific targeting:

-   **`tasks/rpg_games.json`**: Defines a curated list of RPG video games, their Twitch `category_id` (where known), priority, and descriptive tags. This helps the system identify and prioritize streams playing these specific games within the broader RPG category.
-   **`tasks/rpg_streamers.json`**: Lists priority RPG streamers, categorized by tabletop or video games. It includes their channel names, typical games, and a priority level. This allows the system to favor streams from these specific content creators.

Additionally, `tasks/gemini_prompt_templates.md` contains game-genre-specific prompt structures that help the Gemini API tailor its analysis and message suggestions.

## 2. How Targeting Information is Used

### 2.1. `RPGStreamManager` - Stream Selection & Prioritization

1.  **Initial Discovery**: The `RPGStreamManager` fetches live streams from Twitch, primarily looking at the general RPG category.
2.  **Game Prioritization (`rpg_games.json`)**:
    *   It cross-references the discovered streams with the `games` listed in `rpg_games.json`.
    *   Streams playing games with higher `priority` in this file are given preference.
    *   The `category_id` in `rpg_games.json` helps confirm if a stream title matches an actual game category on Twitch (though Twitch's main RPG category is usually the primary filter).
    *   `tags` (e.g., "open-world", "jrpg", "story-driven") can be used for more nuanced filtering or for selecting appropriate Gemini prompt templates later.
3.  **Streamer Prioritization (`rpg_streamers.json`)**:
    *   The manager checks if any discovered RPG streams are hosted by streamers listed in `rpg_streamers.json`.
    *   Streams from higher `priority` streamers are favored.
    *   This allows targeting specific communities or content styles.
4.  **Balancing Algorithm**: The `RPGStreamManager` employs an algorithm to balance agent distribution across:
    *   Top-priority games from `rpg_games.json`.
    *   Top-priority streamers from `rpg_streamers.json`.
    *   Other general RPG streams to ensure diversity and avoid over-concentration.
    *   It considers factors like current viewer counts on streams and the number of agents already assigned to a particular game or streamer.

### 2.2. `GeminiClient` - Contextual Analysis & Message Generation

1.  **Game Context Injection**: When an agent is viewing a stream, the `RPGStreamManager` provides game-specific metadata to the `GeminiClient` (or the agent itself, which then passes it to the client).
    *   This metadata includes the game name, tags from `rpg_games.json`, and potentially the game type (e.g., "MMORPG", "Action RPG", "Tabletop RPG").
2.  **Prompt Template Selection**: Based on the game type or specific game tags, an appropriate prompt template is chosen from the structures defined in `tasks/gemini_prompt_templates.md`.
    *   For example, if an agent is watching "World of Warcraft" (tagged "mmorpg"), the `mmorpgTemplate` would be used.
    *   If it's a generic RPG without a specific template, a base RPG template is used.
3.  **Enhanced Analysis**: The selected prompt template, filled with current stream data (screenshot, audio, chat) AND the game-specific context, is sent to the Gemini API.
    *   The game-specific instructions and keywords in the template guide Gemini to focus its analysis on relevant aspects of that game or genre (e.g., roleplay elements in GTA RP, raid mechanics in WoW).
    *   This leads to more nuanced `analysis` (game state, streamer mood) and more authentic `suggestedMessage` generation.
4.  **Relevant Terminology**: The game-specific prompts encourage Gemini to use appropriate terminology and references that resonate with viewers of that particular game.

## 3. Strategies for Updating Targeting Information

To keep the system effective and targeting relevant content, these configuration files should be periodically reviewed and updated.

### 3.1. Updating `tasks/rpg_games.json`

-   **New & Trending RPGs**: Monitor Twitch and gaming news for new or resurging RPGs.
    *   Add new entries with `name`, estimated `priority` (1=highest), and relevant `tags`.
    *   Try to find the official Twitch `category_id` for the game if it has one separate from the general RPG category. This improves matching accuracy.
-   **Priority Adjustments**: Review the `priority` of existing games based on their current popularity and viewership on Twitch. Downgrade games that are no longer trending.
-   **Tag Refinement**: Add or refine `tags` to better categorize games. This can improve the selection of Gemini prompt templates and allow for more granular targeting if needed in the future.
-   **`last_updated`**: Update the `last_updated` field in the JSON file after making changes.

### 3.2. Updating `tasks/rpg_streamers.json`

-   **New & Popular RPG Streamers**: Identify emerging or popular streamers focusing on RPG content (both video games and tabletop).
    *   Add their `channel` name, assign a `priority`, list their common `games`/`type`, and `schedule` if known.
-   **Streamer Relevance**: Periodically check if listed streamers are still active and primarily streaming RPG content. Adjust priorities or remove inactive/irrelevant streamers.
-   **Distribution Balance**: Ensure a good mix of streamers across different types of RPGs if diverse targeting is desired.
-   **`last_updated`**: Update the `last_updated` field.

### 3.3. Updating `tasks/gemini_prompt_templates.md`

-   **New Game Genres/Types**: If the system starts targeting a significantly new type of RPG not covered by existing templates (e.g., a unique hybrid genre), consider creating a new specialized prompt template in `gemini_prompt_templates.md`.
-   **Prompt Optimization**: Based on the quality of messages generated by agents, existing prompt templates can be refined.
    *   If analysis for a certain game type seems off, review the corresponding template in `gemini_prompt_templates.md` and adjust its system message or user prompt structure to provide better guidance to Gemini.
    *   Consider adding more specific keywords or questions related to that game genre.

### 3.4. General Process for Updates

1.  **Research**: Identify new games, streamers, or trends in the RPG category on Twitch.
2.  **Edit JSON**: Carefully edit `rpg_games.json` and `rpg_streamers.json` using a text editor that validates JSON syntax.
3.  **Validate**: After editing, use a JSON validator to ensure the files are still correctly formatted.
4.  **Review Prompts**: If adding significantly new game types, review `gemini_prompt_templates.md` to see if a new template or modifications are needed.
5.  **Deploy Changes**: The application will typically need to be restarted for the `RPGStreamManager` and `GeminiClient` to pick up changes in these configuration files (unless dynamic reloading is implemented).
    *   In a distributed setup, ensure updated configuration files are deployed to all relevant nodes (Master for overall strategy, potentially workers if they cache or directly use parts of these configs).
6.  **Monitor**: After updates, closely monitor agent behavior, stream selection, and message quality via the dashboard and logs to ensure the changes have the desired effect.

By maintaining these configuration files and prompt strategies, the Twitch RPG Viewer Fleet Management System can remain adaptive and effective in its goal of authentically engaging with a wide variety of RPG content on Twitch. 