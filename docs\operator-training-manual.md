# Operator Training Manual - Twitch RPG Viewer Fleet Management System

Welcome to the Twitch RPG Viewer Fleet Management System! This manual provides guidance for operators on daily operations, monitoring, and basic troubleshooting.

## 1. System Overview

-   **Purpose**: To orchestrate a fleet of 70-150 browser-based agents that view and interact with Twitch RPG streams using Google Gemini for contextual understanding.
-   **Core Components**: Refer to `docs/architecture-twitch-fleet-manager.md` for a detailed breakdown.
    -   `FleetController`: Manages the overall fleet.
    -   `Agent Instances`: Individual viewers.
    -   `Dashboard`: Your primary monitoring interface.
-   **Operational Modes**:
    -   `Standalone`: All components run on a single machine.
    -   `Distributed`: FleetController runs as a `Master`, delegating agent management to multiple `Worker` nodes.

## 2. Prerequisites for Operators

-   Basic understanding of terminal/command-line usage.
-   Familiarity with Twitch and its RPG category.
-   Access to the machine(s) where the system is deployed.
-   Credentials for accessing the monitoring dashboard (if secured).

## 3. System Startup and Shutdown

### 3.1. Configuration Check

-   **Before starting**: Always ensure the `.env` file is correctly configured. Key settings include API keys, operational parameters, and distributed mode settings if applicable.
-   Refer to: `docs/configuration-guide.md`.

### 3.2. Starting the System

Use the provided start scripts located in the `scripts/` directory.

-   **Standalone Mode**:
    ```bash
    cd /path/to/application
    ./scripts/start-fleet.sh standalone
    # or directly: ./scripts/deploy-standalone.sh
    ```
-   **Distributed Mode - Master Node**:
    ```bash
    cd /path/to/application
    ./scripts/start-fleet.sh master
    # or directly: ./scripts/deploy-master.sh
    ```
    *Note the `MASTER_NODE_URL` output by the script; workers will need this.*

-   **Distributed Mode - Worker Node**:
    ```bash
    cd /path/to/application
    ./scripts/start-fleet.sh worker <MASTER_NODE_URL_FROM_MASTER_OUTPUT>
    # or directly: ./scripts/deploy-worker.sh <MASTER_NODE_URL_FROM_MASTER_OUTPUT>
    ```

-   **Verification**: After starting, check the logs (`./logs/`) and the Dashboard (default: `http://localhost:3000`) to ensure the system initializes correctly and agents begin to launch.

### 3.3. Shutting Down the System

-   **Graceful Shutdown**: Press `Ctrl+C` in the terminal window where the application (or master/worker script) is running.
-   This should trigger the FleetController's cleanup routines.
-   In a distributed setup, shut down worker nodes first, then the master node.
-   **Verification**: Ensure all agent browser windows close and the Node.js processes terminate.

## 4. Daily Operations & Monitoring

Your primary tool for daily operations is the **Monitoring Dashboard**.

### 4.1. Accessing the Dashboard

-   Open a web browser and navigate to the URL specified by `DASHBOARD_PORT` in `.env` (e.g., `http://localhost:3000`).

### 4.2. Key Dashboard Sections and What to Monitor

-   **Fleet Overview/Status**:
    -   `Total Agents`: Should be at or near `minAgents`/`maxAgents` (depending on scale level and current load).
    -   `Active/Idle/Error Agents`: Monitor for a high number of error agents.
    -   `Current Scale Level` & `Target Utilization`: Understand the current operational parameters.
    -   `Worker Node Details` (Distributed Mode): Check status, load, and capacity of each worker.
-   **Agent List/Table**:
    -   `Status`: Look for agents stuck in `pending_delegation`, `error`, or `terminating` states for extended periods.
    -   `Stream ID / Game`: Verify agents are on appropriate RPG streams.
    -   `Last Activity / Messages Sent`: Ensure agents are active and interacting as expected.
    -   `Node ID / Worker ID`: Identify which machine an agent is running on.
-   **Individual Agent Details (if available on dashboard)**:
    -   Specific logs, error messages, or performance metrics for an agent.
    -   Some setups might allow viewing the agent's browser window directly or via a VNC/RDP connection to the host machine.

### 4.3. Routine Checks

-   **Morning Check**: Review fleet overview, error counts, and logs for any overnight issues.
-   **Mid-day Check**: Monitor agent distribution and message activity.
-   **End-of-day Check**: Ensure system stability and address any new errors.
-   **Resource Monitoring**: Periodically check CPU/Memory usage on the host machine(s), especially if performance degradation is noticed. High usage may require adjusting `MAX_AGENTS` or scale level settings.

## 5. Basic Troubleshooting

Refer to `docs/troubleshooting-guide.md` for detailed troubleshooting steps.

**Common First Steps for Operators:**

1.  **Identify the Scope**: Is the issue affecting a single agent, multiple agents, a worker node, or the entire fleet?
2.  **Check Logs**: Look for recent `ERROR` or `WARN` messages in the relevant component logs (FleetController, specific Worker, specific Agent if accessible).
3.  **Consult Dashboard**: What information does the dashboard provide about the problematic component(s)?

**Quick Fixes for Common Scenarios:**

-   **Single Agent Error**: The system is designed to retry or recreate errored agents automatically. Monitor if it recovers.
    -   If an agent is persistently erroring, check its logs (if accessible via dashboard or file system) for specific error messages (e.g., Twitch login issue, Gemini API error).
-   **Multiple Agents Failing**: This could indicate a wider issue:
    -   **Twitch Accounts**: Mass login failures? Check `ACCOUNTS_PATH` file. Accounts might be locked or require CAPTCHA.
    -   **Gemini API**: Mass message generation failures? Check `GEMINI_API_KEY` and Google Cloud Console for API status/billing.
    -   **Network Issues**: Is there a general network problem affecting connectivity to Twitch or Google?
    -   **Master/Worker Communication (Distributed)**: If workers are erroring out or not taking tasks, check connectivity to the Master and Master's logs.
-   **Dashboard Unresponsive**: Ensure the `DashboardServer` process is running. Check its logs. Try restarting the main application.

**When to Escalate**: If basic troubleshooting steps don't resolve the issue, or if critical errors persist (e.g., fleet unable to maintain minimum agent count, widespread API failures), escalate to senior support or development team with information collected (see Reporting Issues in `troubleshooting-guide.md`).

## 6. System Maintenance

### 6.1. Backups

-   Regular backups are crucial for data recovery.
-   The `scripts/backup-data.sh` script can be used to back up the database and critical configuration files.
-   Establish a schedule for running backups (e.g., daily).
-   Store backups in a secure, separate location.

### 6.2. Restoring from Backup

-   In case of data loss or corruption, use the `scripts/restore-data.sh` script.
-   **Important**: Always stop the application before restoring data.
-   Refer to the script's usage notes and `docs/troubleshooting-guide.md`.

### 6.3. Log Management

-   Log files can grow large over time. Implement a log rotation strategy or periodically archive/delete old logs from `LOG_PATH` to save disk space.

### 6.4. Updating the Application

-   Follow specific update instructions provided by the development team.
-   Typically involves:
    1.  Stopping the current application.
    2.  Backing up data (see 6.1).
    3.  Fetching the new code version (e.g., `git pull`).
    4.  Installing any new dependencies (`npm install`).
    5.  Running database migrations if any (`npm run migrate` - if such a script exists).
    6.  Restarting the application.

## 7. Security Best Practices for Operators

-   Do not share API keys or account credentials.
-   Ensure the machine(s) running the system are secured and patched.
-   Be cautious of phishing attempts if managing Twitch accounts directly.
-   Report any suspicious activity or security concerns immediately.

This manual provides a starting point for operating the Twitch RPG Viewer Fleet Management System. Always refer to the more detailed documentation files for specific components and procedures. 