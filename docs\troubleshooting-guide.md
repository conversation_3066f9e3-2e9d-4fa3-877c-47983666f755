# Troubleshooting Guide for Twitch RPG Viewer Fleet Management System

This guide helps diagnose and resolve common issues encountered while running the Twitch RPG Viewer Fleet Management System.

## 1. General Checks & Initial Diagnosis

Before diving into specific issues, always perform these initial checks:

1.  **Check Logs**: The primary source of information. Log files are typically located in the directory specified by `LOG_PATH` in your `.env` file (default: `./logs`).
    *   Look for `ERROR` or `WARN` level messages.
    *   In distributed mode, check logs on both the Master and relevant Worker nodes.
2.  **Check Dashboard**: The monitoring dashboard (accessible at `http://localhost:<DASHBOARD_PORT>`) provides real-time status of agents, fleet health, and potential alerts.
3.  **Verify Configuration (`.env`)**: Ensure all required API keys, paths, and operational settings in your `.env` file are correct. Refer to `docs/configuration-guide.md`.
    *   Pay special attention to `DISTRIBUTED_ENABLED`, `NODE_ROLE`, `MASTER_NODE_URL`, and `NODE_PORT` if running in distributed mode.
4.  **Resource Usage**: Check CPU, memory, and network usage on the machine(s) running the application. High resource consumption can lead to various problems.
5.  **Connectivity**: Ensure the machine has stable internet access and can reach Twitch and Google Gemini APIs.

## 2. Common Issues and Solutions

### 2.1. Application Fails to Start

-   **Symptom**: The `npm start` command exits immediately or throws an error in the console.
-   **Diagnosis**:
    *   Check console output for specific error messages.
    *   Ensure Node.js and npm are installed correctly and versions are compatible.
    *   Verify `package.json` is not corrupted and all dependencies listed are valid.
-   **Solution**:
    *   Address any specific errors shown in the console.
    *   Try deleting `node_modules` and `package-lock.json`, then run `npm install` again.
    *   Ensure all critical paths in `.env` (e.g., `DATABASE_PATH`, `ACCOUNTS_PATH`) are accessible.
    *   Check for port conflicts if the `DASHBOARD_PORT` or `NODE_PORT` is already in use.

### 2.2. Agents Not Launching or Stuck in Pending State

-   **Symptom**: Dashboard shows fewer agents than `minAgents` or `targetAgents`, or agents are stuck in a "pending" or "initializing" state.
-   **Diagnosis**:
    *   Check FleetController logs for errors during agent creation (`createAgent`, `_createAgentLocally`).
    *   If distributed, check Master FC logs for worker selection issues and Worker FC logs for errors in `handleMasterCommand_CreateAgent` or `_createAgentLocally`.
    *   Verify `ACCOUNTS_PATH` in `.env` points to a valid JSON file with correct Twitch account credentials.
    *   Ensure `MAX_AGENTS` (global or per-scale-level) is not set too low or already reached on the node.
-   **Solution**:
    *   Resolve any errors found in logs related to agent creation or credential loading.
    *   Ensure account credentials are valid and not locked by Twitch.
    *   Check for resource limitations (CPU/memory) that might prevent new browser instances from launching.

### 2.3. Twitch Login Failures

-   **Symptom**: Agents report errors related to Twitch login, or fail to authenticate.
-   **Diagnosis**:
    *   Inspect individual agent logs or browser windows (if visible) for Twitch login page errors (e.g., CAPTCHAs, incorrect password, account locked, 2FA required).
    *   Check `ACCOUNTS_PATH` file for typos or incorrect credential format.
-   **Solution**:
    *   Manually verify the problematic Twitch account credentials by logging in through a regular browser.
    *   Update credentials in the accounts file.
    *   If CAPTCHAs are frequent, this might indicate suspicious activity from the IP; consider IP rotation or reducing login frequency.
    *   Ensure accounts do not have 2FA enabled unless the system is designed to handle it (which is typically not the case for automated agents).

### 2.4. Gemini API Errors / Agents Not Generating Messages

-   **Symptom**: Agents are viewing streams but not sending messages, or logs show errors related to Gemini API.
-   **Diagnosis**:
    *   Verify `GEMINI_API_KEY` in `.env` is correct and the API is enabled for your Google Cloud project.
    *   Check GeminiClient logs for API request/response errors (e.g., authentication failure, rate limits, invalid requests, billing issues).
    *   Ensure `gemini_prompt_templates.md` and game-specific logic are correctly selecting/formatting prompts.
    *   Inspect the `multimodalInput` being sent to `GC.analyzeStreamContent()` to ensure data (screenshot, audio, chat) is being captured correctly.
-   **Solution**:
    *   Correct `GEMINI_API_KEY` if necessary.
    *   Check Google Cloud Console for API status and billing.
    *   Address any specific errors from the Gemini API (e.g., adjust prompt complexity, check content policies).
    *   Ensure the machine has proper network connectivity to Google Cloud services.

### 2.5. Distributed Mode Issues (Master/Worker)

-   **Symptom (Worker)**: Worker fails to register with Master, or Master doesn't see the Worker.
    -   **Diagnosis**: Worker logs show errors connecting to `MASTER_NODE_URL`. Master logs show no registration attempt from the worker's IP/ID.
    -   **Solution**: Verify `MASTER_NODE_URL` on Worker is correct and reachable. Check firewall settings on both Master and Worker machines. Ensure Master's `NODE_PORT` is open and Master FC is listening.

-   **Symptom (Master)**: Master cannot delegate agents to Workers, or Workers don't pick up tasks.
    -   **Diagnosis**: Master logs show "No available/suitable workers". Worker logs show no commands received from Master.
    -   **Solution**: Ensure Workers are registered and sending healthy heartbeats. Check Worker capacity (`MAX_AGENTS` on worker) and current load. Verify Master's `selectWorkerForNewAgent` logic.

-   **Symptom**: Inconsistent state between Master and Worker (e.g., agent shown on Master but not running on Worker).
    -   **Diagnosis**: Compare agent lists/statuses on Master dashboard vs. Worker logs. Look for errors in heartbeat processing or command acknowledgement.
    -   **Solution**: May require manual intervention or improved sync logic. Check for network interruptions that could cause lost messages.

### 2.6. Agents Exiting Streams Unexpectedly

-   **Symptom**: Agents leave streams shortly after joining or at random intervals.
-   **Diagnosis**:
    *   Check agent logs for reasons (e.g., stream changed category, error encountered, command from FC).
    *   Verify RPGStreamManager's logic for category checking (`CATEGORY_CHECK_INTERVAL` in `.env`).
    *   Ensure the stream is consistently in the RPG category on Twitch.
-   **Solution**:
    *   Adjust `CATEGORY_CHECK_INTERVAL` if too aggressive.
    *   If streams are miscategorized, this is an external Twitch issue.

### 2.7. Performance Issues (High CPU/Memory)

-   **Symptom**: System runs slowly, agents are unresponsive, or machine becomes unstable.
-   **Diagnosis**:
    *   Use system monitoring tools (Task Manager, htop) to identify resource bottlenecks.
    *   Profile the Node.js application if specific functions are suspected.
    *   Check number of browser instances (agents) vs. machine capacity.
-   **Solution**:
    *   Reduce `MAX_AGENTS` if the machine is overloaded.
    *   Increase `AGENT_LAUNCH_INTERVAL` to stagger resource demand.
    *   Optimize content capture methods (e.g., lower screenshot frequency/quality if permissible).
    *   Ensure no memory leaks in agent browser processes or Node.js code.
    *   Consider distributing the fleet across more Worker nodes if in distributed mode.

### 2.8. Incorrect Stream Targeting / Game Identification

-   **Symptom**: Agents are joining non-RPG streams or not prioritizing specified games/streamers.
-   **Diagnosis**:
    *   Review `rpg_games.json` and `rpg_streamers.json` for correct game titles, category IDs, and streamer channel names.
    *   Check RPGStreamManager logs for how it's fetching and filtering streams.
    *   Verify Twitch API client is correctly identifying stream categories and metadata.
-   **Solution**:
    *   Update `rpg_games.json` and `rpg_streamers.json` with accurate data.
    *   Debug RSM logic if filtering or prioritization is not working as expected.

## 3. Advanced Troubleshooting

-   **Enable Verbose Logging**: Set `DEBUG=true` in `.env` for more detailed logs from various components.
-   **Individual Agent Inspection**: If possible (e.g., in development or with fewer agents), directly observe the browser window of a problematic agent to see what it's doing or what errors are displayed on the page.
-   **Database Inspection**: Use a SQLite browser to inspect the database (`DATABASE_PATH`) to check agent states, logs, and metrics directly.

## 4. Reporting Issues

If you cannot resolve an issue, please gather the following information before seeking help or reporting a bug:

-   Version of the application.
-   Operating System and Node.js version.
-   Relevant sections from your `.env` file (censor API keys).
-   Detailed steps to reproduce the issue.
-   Relevant log snippets from all affected components (Master, Worker, Agent).
-   Screenshots from the Dashboard or agent browser windows if applicable. 