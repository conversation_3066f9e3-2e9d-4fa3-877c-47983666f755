#!/usr/bin/env node

/**
 * Twitch Fleet Manager - Main Entry Point
 * 
 * This is the main entry point for the Twitch RPG Fleet Management System.
 * It initializes and starts all core components including:
 * - Fleet Controller (manages agents)
 * - Dashboard Server (monitoring UI)
 * - Database initialization
 * - Agent creation and management
 */

const path = require('path');
require('dotenv').config();

// Import core components
const FleetController = require('./src/controller/FleetController');
const DashboardServer = require('./src/dashboard/DashboardServer');
const DatabaseManager = require('./src/database/DatabaseManager');
const Logger = require('./src/utils/Logger');
const ConfigManager = require('./src/config/ConfigManager');

class FleetManagerApp {
  constructor() {
    this.fleetController = null;
    this.dashboardServer = null;
    this.isShuttingDown = false;
    
    // Configuration
    this.config = new ConfigManager();
    this.maxAgents = parseInt(process.env.MAX_AGENTS || '10'); // Start with fewer agents for testing
    this.isDevelopment = process.env.NODE_ENV === 'development';
    
    Logger.info('Fleet Manager App initializing', {
      maxAgents: this.maxAgents,
      isDevelopment: this.isDevelopment,
      nodeEnv: process.env.NODE_ENV
    });
  }

  async initialize() {
    try {
      Logger.info('Starting Twitch Fleet Management System...');
      
      // Initialize database
      await this.initializeDatabase();
      
      // Initialize dashboard server
      await this.initializeDashboard();
      
      // Initialize fleet controller
      await this.initializeFleetController();
      
      // Set up graceful shutdown
      this.setupGracefulShutdown();
      
      Logger.info('Fleet Management System started successfully!', {
        dashboardUrl: `http://localhost:${process.env.DASHBOARD_PORT || 3000}`,
        maxAgents: this.maxAgents
      });
      
      // Start creating agents after a short delay
      setTimeout(() => {
        this.startAgentCreation();
      }, 5000);
      
    } catch (error) {
      Logger.error('Failed to initialize Fleet Management System', { error: error.message });
      process.exit(1);
    }
  }

  async initializeDatabase() {
    try {
      Logger.info('Initializing database...');
      // DatabaseManager is already initialized as a singleton
      // Just ensure tables exist
      await DatabaseManager.createTables();
      Logger.info('Database initialized successfully');
    } catch (error) {
      Logger.error('Database initialization failed', { error: error.message });
      throw error;
    }
  }

  async initializeDashboard() {
    try {
      Logger.info('Initializing dashboard server...');
      this.dashboardServer = DashboardServer; // It's already an instance
      await this.dashboardServer.initialize();
      Logger.info('Dashboard server started successfully');
    } catch (error) {
      Logger.error('Dashboard initialization failed', { error: error.message });
      throw error;
    }
  }

  async initializeFleetController() {
    try {
      Logger.info('Initializing fleet controller...');
      this.fleetController = FleetController; // It's already an instance
      await this.fleetController.initialize();
      Logger.info('Fleet controller initialized successfully');
    } catch (error) {
      Logger.error('Fleet controller initialization failed', { error: error.message });
      throw error;
    }
  }

  async startAgentCreation() {
    if (!this.fleetController || this.isShuttingDown) {
      return;
    }

    try {
      Logger.info('Starting agent creation process...');

      // Create initial batch of agents
      const initialAgents = Math.min(5, this.maxAgents); // Start with 5 agents

      for (let i = 0; i < initialAgents; i++) {
        if (this.isShuttingDown) break;

        Logger.info(`Creating agent ${i + 1}/${initialAgents}...`);
        try {
          await this.fleetController.createAgent();
          Logger.info(`Agent ${i + 1}/${initialAgents} created successfully`);
        } catch (error) {
          Logger.error('Error during agent creation', { error: error.message });
          // Continue with other agents even if one fails
        }

        // Wait between agent creation to avoid overwhelming the system
        await new Promise(resolve => setTimeout(resolve, 1000));
      }

      Logger.info(`Initial agent creation complete. Created ${initialAgents} agents.`);
      
    } catch (error) {
      Logger.error('Error during agent creation', { error: error.message });
    }
  }

  setupGracefulShutdown() {
    const shutdown = async (signal) => {
      if (this.isShuttingDown) {
        Logger.warn('Shutdown already in progress...');
        return;
      }
      
      this.isShuttingDown = true;
      Logger.info(`Received ${signal}. Starting graceful shutdown...`);
      
      try {
        // Stop fleet controller
        if (this.fleetController) {
          Logger.info('Shutting down fleet controller...');
          await this.fleetController.cleanup();
        }
        
        // Stop dashboard server
        if (this.dashboardServer) {
          Logger.info('Shutting down dashboard server...');
          await this.dashboardServer.cleanup();
        }
        
        // Close database connections
        Logger.info('Closing database connections...');
        await DatabaseManager.cleanup();
        
        Logger.info('Graceful shutdown complete');
        process.exit(0);
        
      } catch (error) {
        Logger.error('Error during shutdown', { error: error.message });
        process.exit(1);
      }
    };

    // Handle various shutdown signals
    process.on('SIGINT', () => shutdown('SIGINT'));
    process.on('SIGTERM', () => shutdown('SIGTERM'));
    process.on('SIGQUIT', () => shutdown('SIGQUIT'));
    
    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
      Logger.error('Uncaught exception', { error: error.message, stack: error.stack });
      shutdown('uncaughtException');
    });
    
    process.on('unhandledRejection', (reason, promise) => {
      Logger.error('Unhandled rejection', { reason, promise });
      shutdown('unhandledRejection');
    });
  }
}

// Initialize the fleet management system
async function main() {
  const fleetManager = new FleetManagerApp();
  await fleetManager.initialize();
}

// Start the application
main().catch((error) => {
  console.error('Failed to start Fleet Management System:', error);
  process.exit(1);
});

// Export for testing
module.exports = FleetManagerApp;
