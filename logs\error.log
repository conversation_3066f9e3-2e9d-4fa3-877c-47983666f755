{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Failed to load game data","service":"fleet-manager","timestamp":"2025-06-06T00:11:52.668Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Failed to initialize Game Metadata Repository","service":"fleet-manager","timestamp":"2025-06-06T00:11:52.778Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Failed to discover RPG streams","service":"fleet-manager","timestamp":"2025-06-06T00:11:52.848Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Stream refresh failed","service":"fleet-manager","timestamp":"2025-06-06T00:11:52.890Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Stream refresh failed","service":"fleet-manager","timestamp":"2025-06-06T00:11:52.930Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Failed to load game data","service":"fleet-manager","timestamp":"2025-06-06T06:14:00.194Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Failed to initialize Game Metadata Repository","service":"fleet-manager","timestamp":"2025-06-06T06:14:00.225Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Failed to discover RPG streams","service":"fleet-manager","timestamp":"2025-06-06T06:14:00.239Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Stream refresh failed","service":"fleet-manager","timestamp":"2025-06-06T06:14:00.257Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Stream refresh failed","service":"fleet-manager","timestamp":"2025-06-06T06:14:00.269Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Failed to load game data","service":"fleet-manager","timestamp":"2025-06-06T06:19:00.114Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Failed to initialize Game Metadata Repository","service":"fleet-manager","timestamp":"2025-06-06T06:19:00.115Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Failed to discover RPG streams","service":"fleet-manager","timestamp":"2025-06-06T06:19:00.115Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Stream refresh failed","service":"fleet-manager","timestamp":"2025-06-06T06:19:00.115Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Stream refresh failed","service":"fleet-manager","timestamp":"2025-06-06T06:19:00.116Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Failed to load game data","service":"fleet-manager","timestamp":"2025-06-06T06:24:00.124Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Failed to initialize Game Metadata Repository","service":"fleet-manager","timestamp":"2025-06-06T06:24:00.124Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Failed to discover RPG streams","service":"fleet-manager","timestamp":"2025-06-06T06:24:00.125Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Stream refresh failed","service":"fleet-manager","timestamp":"2025-06-06T06:24:00.125Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Stream refresh failed","service":"fleet-manager","timestamp":"2025-06-06T06:24:00.126Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Failed to load game data","service":"fleet-manager","timestamp":"2025-06-06T06:29:00.125Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Failed to initialize Game Metadata Repository","service":"fleet-manager","timestamp":"2025-06-06T06:29:00.125Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Failed to discover RPG streams","service":"fleet-manager","timestamp":"2025-06-06T06:29:00.126Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Stream refresh failed","service":"fleet-manager","timestamp":"2025-06-06T06:29:00.126Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Stream refresh failed","service":"fleet-manager","timestamp":"2025-06-06T06:29:00.126Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Failed to load game data","service":"fleet-manager","timestamp":"2025-06-06T06:34:00.140Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Failed to initialize Game Metadata Repository","service":"fleet-manager","timestamp":"2025-06-06T06:34:00.140Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Failed to discover RPG streams","service":"fleet-manager","timestamp":"2025-06-06T06:34:00.141Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Stream refresh failed","service":"fleet-manager","timestamp":"2025-06-06T06:34:00.141Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Stream refresh failed","service":"fleet-manager","timestamp":"2025-06-06T06:34:00.141Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Failed to load game data","service":"fleet-manager","timestamp":"2025-06-06T06:39:00.150Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Failed to initialize Game Metadata Repository","service":"fleet-manager","timestamp":"2025-06-06T06:39:00.152Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Failed to discover RPG streams","service":"fleet-manager","timestamp":"2025-06-06T06:39:00.152Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Stream refresh failed","service":"fleet-manager","timestamp":"2025-06-06T06:39:00.153Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Stream refresh failed","service":"fleet-manager","timestamp":"2025-06-06T06:39:00.154Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Failed to load game data","service":"fleet-manager","timestamp":"2025-06-06T06:44:00.151Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Failed to initialize Game Metadata Repository","service":"fleet-manager","timestamp":"2025-06-06T06:44:00.152Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Failed to discover RPG streams","service":"fleet-manager","timestamp":"2025-06-06T06:44:00.153Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Stream refresh failed","service":"fleet-manager","timestamp":"2025-06-06T06:44:00.153Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Stream refresh failed","service":"fleet-manager","timestamp":"2025-06-06T06:44:00.154Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Failed to load game data","service":"fleet-manager","timestamp":"2025-06-06T06:49:00.153Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Failed to initialize Game Metadata Repository","service":"fleet-manager","timestamp":"2025-06-06T06:49:00.154Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Failed to discover RPG streams","service":"fleet-manager","timestamp":"2025-06-06T06:49:00.155Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Stream refresh failed","service":"fleet-manager","timestamp":"2025-06-06T06:49:00.156Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Stream refresh failed","service":"fleet-manager","timestamp":"2025-06-06T06:49:00.156Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Failed to load game data","service":"fleet-manager","timestamp":"2025-06-06T06:54:00.163Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Failed to initialize Game Metadata Repository","service":"fleet-manager","timestamp":"2025-06-06T06:54:00.164Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Failed to discover RPG streams","service":"fleet-manager","timestamp":"2025-06-06T06:54:00.164Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Stream refresh failed","service":"fleet-manager","timestamp":"2025-06-06T06:54:00.164Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Stream refresh failed","service":"fleet-manager","timestamp":"2025-06-06T06:54:00.165Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Failed to load game data","service":"fleet-manager","timestamp":"2025-06-06T06:59:00.168Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Failed to initialize Game Metadata Repository","service":"fleet-manager","timestamp":"2025-06-06T06:59:00.168Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Failed to discover RPG streams","service":"fleet-manager","timestamp":"2025-06-06T06:59:00.169Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Stream refresh failed","service":"fleet-manager","timestamp":"2025-06-06T06:59:00.170Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Stream refresh failed","service":"fleet-manager","timestamp":"2025-06-06T06:59:00.171Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Failed to load game data","service":"fleet-manager","timestamp":"2025-06-06T07:04:00.184Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Failed to initialize Game Metadata Repository","service":"fleet-manager","timestamp":"2025-06-06T07:04:00.185Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Failed to discover RPG streams","service":"fleet-manager","timestamp":"2025-06-06T07:04:00.186Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Stream refresh failed","service":"fleet-manager","timestamp":"2025-06-06T07:04:00.186Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Stream refresh failed","service":"fleet-manager","timestamp":"2025-06-06T07:04:00.188Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Failed to load game data","service":"fleet-manager","timestamp":"2025-06-06T07:32:41.276Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Failed to initialize Game Metadata Repository","service":"fleet-manager","timestamp":"2025-06-06T07:32:41.295Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Failed to discover RPG streams","service":"fleet-manager","timestamp":"2025-06-06T07:32:41.311Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Stream refresh failed","service":"fleet-manager","timestamp":"2025-06-06T07:32:41.325Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Stream refresh failed","service":"fleet-manager","timestamp":"2025-06-06T07:32:41.339Z"}
