{"level":"info","message":"Database initialized","path":"D:\\TW\\data\\fleet.db","service":"fleet-manager","timestamp":"2025-06-05T23:11:08.773Z"}
{"activeScaleParams":{},"level":"info","message":"FleetController initializing with scale level: \"medium\"","service":"fleet-manager","sourceScaleLevelsConfig":{},"timestamp":"2025-06-05T23:11:08.916Z"}
{"currentScaleLevelName":"medium","level":"info","maxAgents":10,"message":"FleetController effective scaling parameters:","minAgents":7,"scaleDownBatchSize":3,"scaleUpBatchSize":5,"service":"fleet-manager","targetUtilization":0.8,"timestamp":"2025-06-05T23:11:08.917Z"}
{"isDevelopment":true,"level":"info","maxAgents":10,"message":"Fleet Manager App initializing","nodeEnv":"development","service":"fleet-manager","timestamp":"2025-06-05T23:11:09.206Z"}
{"level":"info","message":"Starting Twitch Fleet Management System...","service":"fleet-manager","timestamp":"2025-06-05T23:11:09.207Z"}
{"level":"info","message":"Initializing database...","service":"fleet-manager","timestamp":"2025-06-05T23:11:09.208Z"}
{"level":"info","message":"Database tables created successfully","service":"fleet-manager","timestamp":"2025-06-05T23:11:09.221Z"}
{"level":"info","message":"Database initialized successfully","service":"fleet-manager","timestamp":"2025-06-05T23:11:09.222Z"}
{"level":"info","message":"Initializing dashboard server...","service":"fleet-manager","timestamp":"2025-06-05T23:11:09.222Z"}
{"level":"info","message":"Initializing Dashboard Server","service":"fleet-manager","timestamp":"2025-06-05T23:11:09.223Z"}
{"level":"warn","message":"UI build directory not found, skipping static file serving","service":"fleet-manager","timestamp":"2025-06-05T23:11:09.225Z"}
{"level":"info","message":"Dashboard server listening on localhost:3000","service":"fleet-manager","timestamp":"2025-06-05T23:11:09.254Z"}
{"level":"info","message":"Dashboard Server initialized successfully","service":"fleet-manager","timestamp":"2025-06-05T23:11:09.255Z"}
{"level":"info","message":"Dashboard server started successfully","service":"fleet-manager","timestamp":"2025-06-05T23:11:09.255Z"}
{"level":"info","message":"Initializing fleet controller...","service":"fleet-manager","timestamp":"2025-06-05T23:11:09.255Z"}
{"level":"info","message":"Initializing Fleet Controller","service":"fleet-manager","timestamp":"2025-06-05T23:11:09.256Z"}
{"level":"info","message":"Distributed mode: false, Role: master","service":"fleet-manager","timestamp":"2025-06-05T23:11:09.256Z"}
{"level":"info","message":"Performing standard Fleet Controller initialization.","service":"fleet-manager","timestamp":"2025-06-05T23:11:09.256Z"}
{"count":1,"level":"info","message":"Loaded existing agents","service":"fleet-manager","timestamp":"2025-06-05T23:11:09.260Z"}
{"level":"info","maxAgents":10,"message":"Fleet Controller initialized successfully","minAgents":7,"service":"fleet-manager","targetUtilization":0.8,"timestamp":"2025-06-05T23:11:09.260Z"}
{"level":"info","message":"Fleet controller initialized successfully","service":"fleet-manager","timestamp":"2025-06-05T23:11:09.261Z"}
{"dashboardUrl":"http://localhost:3000","level":"info","maxAgents":10,"message":"Fleet Management System started successfully!","service":"fleet-manager","timestamp":"2025-06-05T23:11:09.261Z"}
{"level":"info","message":"Starting agent creation process...","service":"fleet-manager","timestamp":"2025-06-05T23:11:14.270Z"}
{"level":"info","message":"Creating agent 1/5...","service":"fleet-manager","timestamp":"2025-06-05T23:11:14.270Z"}
{"level":"info","message":"Node master-node (Role: master) creating agent agent-1749165074271-yv9r7 locally.","service":"fleet-manager","timestamp":"2025-06-05T23:11:14.271Z"}
{"agentId":"agent-1749165074271-yv9r7","level":"info","message":"Agent created successfully","nodeId":"master-node","service":"fleet-manager","status":"idle","timestamp":"2025-06-05T23:11:14.278Z"}
{"agentId":"agent-1749165074271-yv9r7","eventType":"created_locally","level":"info","message":"Agent event logged","service":"fleet-manager","timestamp":"2025-06-05T23:11:14.287Z"}
{"level":"info","message":"Agent agent-1749165074271-yv9r7 created successfully on node master-node.","service":"fleet-manager","timestamp":"2025-06-05T23:11:14.287Z"}
{"level":"info","message":"Agent 1/5 created successfully","service":"fleet-manager","timestamp":"2025-06-05T23:11:14.288Z"}
{"level":"info","message":"Creating agent 2/5...","service":"fleet-manager","timestamp":"2025-06-05T23:11:15.293Z"}
{"level":"info","message":"Node master-node (Role: master) creating agent agent-1749165075293-uofgb locally.","service":"fleet-manager","timestamp":"2025-06-05T23:11:15.293Z"}
{"agentId":"agent-1749165075293-uofgb","level":"info","message":"Agent created successfully","nodeId":"master-node","service":"fleet-manager","status":"idle","timestamp":"2025-06-05T23:11:15.300Z"}
{"agentId":"agent-1749165075293-uofgb","eventType":"created_locally","level":"info","message":"Agent event logged","service":"fleet-manager","timestamp":"2025-06-05T23:11:15.306Z"}
{"level":"info","message":"Agent agent-1749165075293-uofgb created successfully on node master-node.","service":"fleet-manager","timestamp":"2025-06-05T23:11:15.307Z"}
{"level":"info","message":"Agent 2/5 created successfully","service":"fleet-manager","timestamp":"2025-06-05T23:11:15.307Z"}
{"level":"info","message":"Creating agent 3/5...","service":"fleet-manager","timestamp":"2025-06-05T23:11:16.315Z"}
{"level":"info","message":"Node master-node (Role: master) creating agent agent-1749165076315-c7i5z locally.","service":"fleet-manager","timestamp":"2025-06-05T23:11:16.315Z"}
{"agentId":"agent-1749165076315-c7i5z","level":"info","message":"Agent created successfully","nodeId":"master-node","service":"fleet-manager","status":"idle","timestamp":"2025-06-05T23:11:16.321Z"}
{"agentId":"agent-1749165076315-c7i5z","eventType":"created_locally","level":"info","message":"Agent event logged","service":"fleet-manager","timestamp":"2025-06-05T23:11:16.327Z"}
{"level":"info","message":"Agent agent-1749165076315-c7i5z created successfully on node master-node.","service":"fleet-manager","timestamp":"2025-06-05T23:11:16.328Z"}
{"level":"info","message":"Agent 3/5 created successfully","service":"fleet-manager","timestamp":"2025-06-05T23:11:16.328Z"}
{"level":"info","message":"Creating agent 4/5...","service":"fleet-manager","timestamp":"2025-06-05T23:11:17.332Z"}
{"level":"info","message":"Node master-node (Role: master) creating agent agent-1749165077333-ds2wq locally.","service":"fleet-manager","timestamp":"2025-06-05T23:11:17.333Z"}
{"agentId":"agent-1749165077333-ds2wq","level":"info","message":"Agent created successfully","nodeId":"master-node","service":"fleet-manager","status":"idle","timestamp":"2025-06-05T23:11:17.341Z"}
{"agentId":"agent-1749165077333-ds2wq","eventType":"created_locally","level":"info","message":"Agent event logged","service":"fleet-manager","timestamp":"2025-06-05T23:11:17.350Z"}
{"level":"info","message":"Agent agent-1749165077333-ds2wq created successfully on node master-node.","service":"fleet-manager","timestamp":"2025-06-05T23:11:17.353Z"}
{"level":"info","message":"Agent 4/5 created successfully","service":"fleet-manager","timestamp":"2025-06-05T23:11:17.356Z"}
{"level":"info","message":"Creating agent 5/5...","service":"fleet-manager","timestamp":"2025-06-05T23:11:18.365Z"}
{"level":"info","message":"Node master-node (Role: master) creating agent agent-1749165078366-0wxrv locally.","service":"fleet-manager","timestamp":"2025-06-05T23:11:18.366Z"}
{"agentId":"agent-1749165078366-0wxrv","level":"info","message":"Agent created successfully","nodeId":"master-node","service":"fleet-manager","status":"idle","timestamp":"2025-06-05T23:11:18.372Z"}
{"agentId":"agent-1749165078366-0wxrv","eventType":"created_locally","level":"info","message":"Agent event logged","service":"fleet-manager","timestamp":"2025-06-05T23:11:18.380Z"}
{"level":"info","message":"Agent agent-1749165078366-0wxrv created successfully on node master-node.","service":"fleet-manager","timestamp":"2025-06-05T23:11:18.381Z"}
{"level":"info","message":"Agent 5/5 created successfully","service":"fleet-manager","timestamp":"2025-06-05T23:11:18.381Z"}
{"level":"info","message":"Initial agent creation complete. Created 5 agents.","service":"fleet-manager","timestamp":"2025-06-05T23:11:19.386Z"}
{"level":"info","message":"Starting stream refresh","service":"fleet-manager","timestamp":"2025-06-06T00:11:52.369Z"}
{"level":"info","message":"Initializing Game Metadata Repository","service":"fleet-manager","timestamp":"2025-06-06T00:11:52.445Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Failed to load game data","service":"fleet-manager","timestamp":"2025-06-06T00:11:52.668Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Failed to initialize Game Metadata Repository","service":"fleet-manager","timestamp":"2025-06-06T00:11:52.778Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Failed to discover RPG streams","service":"fleet-manager","timestamp":"2025-06-06T00:11:52.848Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Stream refresh failed","service":"fleet-manager","timestamp":"2025-06-06T00:11:52.890Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Stream refresh failed","service":"fleet-manager","timestamp":"2025-06-06T00:11:52.930Z"}
{"level":"info","message":"Starting stream refresh","service":"fleet-manager","timestamp":"2025-06-06T06:14:00.103Z"}
{"level":"info","message":"Initializing Game Metadata Repository","service":"fleet-manager","timestamp":"2025-06-06T06:14:00.149Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Failed to load game data","service":"fleet-manager","timestamp":"2025-06-06T06:14:00.194Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Failed to initialize Game Metadata Repository","service":"fleet-manager","timestamp":"2025-06-06T06:14:00.225Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Failed to discover RPG streams","service":"fleet-manager","timestamp":"2025-06-06T06:14:00.239Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Stream refresh failed","service":"fleet-manager","timestamp":"2025-06-06T06:14:00.257Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Stream refresh failed","service":"fleet-manager","timestamp":"2025-06-06T06:14:00.269Z"}
{"level":"info","message":"Starting stream refresh","service":"fleet-manager","timestamp":"2025-06-06T06:19:00.113Z"}
{"level":"info","message":"Initializing Game Metadata Repository","service":"fleet-manager","timestamp":"2025-06-06T06:19:00.113Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Failed to load game data","service":"fleet-manager","timestamp":"2025-06-06T06:19:00.114Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Failed to initialize Game Metadata Repository","service":"fleet-manager","timestamp":"2025-06-06T06:19:00.115Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Failed to discover RPG streams","service":"fleet-manager","timestamp":"2025-06-06T06:19:00.115Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Stream refresh failed","service":"fleet-manager","timestamp":"2025-06-06T06:19:00.115Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Stream refresh failed","service":"fleet-manager","timestamp":"2025-06-06T06:19:00.116Z"}
{"level":"info","message":"Starting stream refresh","service":"fleet-manager","timestamp":"2025-06-06T06:24:00.122Z"}
{"level":"info","message":"Initializing Game Metadata Repository","service":"fleet-manager","timestamp":"2025-06-06T06:24:00.123Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Failed to load game data","service":"fleet-manager","timestamp":"2025-06-06T06:24:00.124Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Failed to initialize Game Metadata Repository","service":"fleet-manager","timestamp":"2025-06-06T06:24:00.124Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Failed to discover RPG streams","service":"fleet-manager","timestamp":"2025-06-06T06:24:00.125Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Stream refresh failed","service":"fleet-manager","timestamp":"2025-06-06T06:24:00.125Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Stream refresh failed","service":"fleet-manager","timestamp":"2025-06-06T06:24:00.126Z"}
{"level":"info","message":"Starting stream refresh","service":"fleet-manager","timestamp":"2025-06-06T06:29:00.124Z"}
{"level":"info","message":"Initializing Game Metadata Repository","service":"fleet-manager","timestamp":"2025-06-06T06:29:00.124Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Failed to load game data","service":"fleet-manager","timestamp":"2025-06-06T06:29:00.125Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Failed to initialize Game Metadata Repository","service":"fleet-manager","timestamp":"2025-06-06T06:29:00.125Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Failed to discover RPG streams","service":"fleet-manager","timestamp":"2025-06-06T06:29:00.126Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Stream refresh failed","service":"fleet-manager","timestamp":"2025-06-06T06:29:00.126Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Stream refresh failed","service":"fleet-manager","timestamp":"2025-06-06T06:29:00.126Z"}
{"level":"info","message":"Starting stream refresh","service":"fleet-manager","timestamp":"2025-06-06T06:34:00.139Z"}
{"level":"info","message":"Initializing Game Metadata Repository","service":"fleet-manager","timestamp":"2025-06-06T06:34:00.139Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Failed to load game data","service":"fleet-manager","timestamp":"2025-06-06T06:34:00.140Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Failed to initialize Game Metadata Repository","service":"fleet-manager","timestamp":"2025-06-06T06:34:00.140Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Failed to discover RPG streams","service":"fleet-manager","timestamp":"2025-06-06T06:34:00.141Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Stream refresh failed","service":"fleet-manager","timestamp":"2025-06-06T06:34:00.141Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Stream refresh failed","service":"fleet-manager","timestamp":"2025-06-06T06:34:00.141Z"}
{"level":"info","message":"Starting stream refresh","service":"fleet-manager","timestamp":"2025-06-06T06:39:00.148Z"}
{"level":"info","message":"Initializing Game Metadata Repository","service":"fleet-manager","timestamp":"2025-06-06T06:39:00.149Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Failed to load game data","service":"fleet-manager","timestamp":"2025-06-06T06:39:00.150Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Failed to initialize Game Metadata Repository","service":"fleet-manager","timestamp":"2025-06-06T06:39:00.152Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Failed to discover RPG streams","service":"fleet-manager","timestamp":"2025-06-06T06:39:00.152Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Stream refresh failed","service":"fleet-manager","timestamp":"2025-06-06T06:39:00.153Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Stream refresh failed","service":"fleet-manager","timestamp":"2025-06-06T06:39:00.154Z"}
{"level":"info","message":"Starting stream refresh","service":"fleet-manager","timestamp":"2025-06-06T06:44:00.148Z"}
{"level":"info","message":"Initializing Game Metadata Repository","service":"fleet-manager","timestamp":"2025-06-06T06:44:00.150Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Failed to load game data","service":"fleet-manager","timestamp":"2025-06-06T06:44:00.151Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Failed to initialize Game Metadata Repository","service":"fleet-manager","timestamp":"2025-06-06T06:44:00.152Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Failed to discover RPG streams","service":"fleet-manager","timestamp":"2025-06-06T06:44:00.153Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Stream refresh failed","service":"fleet-manager","timestamp":"2025-06-06T06:44:00.153Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Stream refresh failed","service":"fleet-manager","timestamp":"2025-06-06T06:44:00.154Z"}
{"level":"info","message":"Starting stream refresh","service":"fleet-manager","timestamp":"2025-06-06T06:49:00.151Z"}
{"level":"info","message":"Initializing Game Metadata Repository","service":"fleet-manager","timestamp":"2025-06-06T06:49:00.152Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Failed to load game data","service":"fleet-manager","timestamp":"2025-06-06T06:49:00.153Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Failed to initialize Game Metadata Repository","service":"fleet-manager","timestamp":"2025-06-06T06:49:00.154Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Failed to discover RPG streams","service":"fleet-manager","timestamp":"2025-06-06T06:49:00.155Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Stream refresh failed","service":"fleet-manager","timestamp":"2025-06-06T06:49:00.156Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Stream refresh failed","service":"fleet-manager","timestamp":"2025-06-06T06:49:00.156Z"}
{"level":"info","message":"Starting stream refresh","service":"fleet-manager","timestamp":"2025-06-06T06:54:00.161Z"}
{"level":"info","message":"Initializing Game Metadata Repository","service":"fleet-manager","timestamp":"2025-06-06T06:54:00.162Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Failed to load game data","service":"fleet-manager","timestamp":"2025-06-06T06:54:00.163Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Failed to initialize Game Metadata Repository","service":"fleet-manager","timestamp":"2025-06-06T06:54:00.164Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Failed to discover RPG streams","service":"fleet-manager","timestamp":"2025-06-06T06:54:00.164Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Stream refresh failed","service":"fleet-manager","timestamp":"2025-06-06T06:54:00.164Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Stream refresh failed","service":"fleet-manager","timestamp":"2025-06-06T06:54:00.165Z"}
{"level":"info","message":"Starting stream refresh","service":"fleet-manager","timestamp":"2025-06-06T06:59:00.166Z"}
{"level":"info","message":"Initializing Game Metadata Repository","service":"fleet-manager","timestamp":"2025-06-06T06:59:00.167Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Failed to load game data","service":"fleet-manager","timestamp":"2025-06-06T06:59:00.168Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Failed to initialize Game Metadata Repository","service":"fleet-manager","timestamp":"2025-06-06T06:59:00.168Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Failed to discover RPG streams","service":"fleet-manager","timestamp":"2025-06-06T06:59:00.169Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Stream refresh failed","service":"fleet-manager","timestamp":"2025-06-06T06:59:00.170Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Stream refresh failed","service":"fleet-manager","timestamp":"2025-06-06T06:59:00.171Z"}
{"level":"info","message":"Starting stream refresh","service":"fleet-manager","timestamp":"2025-06-06T07:04:00.179Z"}
{"level":"info","message":"Initializing Game Metadata Repository","service":"fleet-manager","timestamp":"2025-06-06T07:04:00.182Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Failed to load game data","service":"fleet-manager","timestamp":"2025-06-06T07:04:00.184Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Failed to initialize Game Metadata Repository","service":"fleet-manager","timestamp":"2025-06-06T07:04:00.185Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Failed to discover RPG streams","service":"fleet-manager","timestamp":"2025-06-06T07:04:00.186Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Stream refresh failed","service":"fleet-manager","timestamp":"2025-06-06T07:04:00.186Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Stream refresh failed","service":"fleet-manager","timestamp":"2025-06-06T07:04:00.188Z"}
{"level":"info","message":"Starting stream refresh","service":"fleet-manager","timestamp":"2025-06-06T07:32:41.230Z"}
{"level":"info","message":"Initializing Game Metadata Repository","service":"fleet-manager","timestamp":"2025-06-06T07:32:41.246Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Failed to load game data","service":"fleet-manager","timestamp":"2025-06-06T07:32:41.276Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Failed to initialize Game Metadata Repository","service":"fleet-manager","timestamp":"2025-06-06T07:32:41.295Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Failed to discover RPG streams","service":"fleet-manager","timestamp":"2025-06-06T07:32:41.311Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Stream refresh failed","service":"fleet-manager","timestamp":"2025-06-06T07:32:41.325Z"}
{"error":"ENOENT: no such file or directory, open 'D:\\TW\\config\\rpg_games.json'","level":"error","message":"Stream refresh failed","service":"fleet-manager","timestamp":"2025-06-06T07:32:41.339Z"}
