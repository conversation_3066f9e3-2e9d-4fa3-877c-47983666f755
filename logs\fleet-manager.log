{"level":"info","message":"Database initialized","path":"D:\\TW\\data\\fleet.db","service":"fleet-manager","timestamp":"2025-06-05T23:06:30.511Z"}
{"activeScaleParams":{},"level":"info","message":"FleetController initializing with scale level: \"medium\"","service":"fleet-manager","sourceScaleLevelsConfig":{},"timestamp":"2025-06-05T23:06:30.698Z"}
{"currentScaleLevelName":"medium","level":"info","maxAgents":10,"message":"FleetController effective scaling parameters:","minAgents":7,"scaleDownBatchSize":3,"scaleUpBatchSize":5,"service":"fleet-manager","targetUtilization":0.8,"timestamp":"2025-06-05T23:06:30.699Z"}
{"isDevelopment":true,"level":"info","maxAgents":10,"message":"Fleet Manager App initializing","nodeEnv":"development","service":"fleet-manager","timestamp":"2025-06-05T23:06:31.074Z"}
{"level":"info","message":"Starting Twitch Fleet Management System...","service":"fleet-manager","timestamp":"2025-06-05T23:06:31.075Z"}
{"level":"info","message":"Initializing database...","service":"fleet-manager","timestamp":"2025-06-05T23:06:31.075Z"}
{"level":"info","message":"Database tables created successfully","service":"fleet-manager","timestamp":"2025-06-05T23:06:31.090Z"}
{"level":"info","message":"Database initialized successfully","service":"fleet-manager","timestamp":"2025-06-05T23:06:31.091Z"}
{"level":"info","message":"Initializing dashboard server...","service":"fleet-manager","timestamp":"2025-06-05T23:06:31.092Z"}
{"level":"info","message":"Initializing Dashboard Server","service":"fleet-manager","timestamp":"2025-06-05T23:06:31.093Z"}
{"level":"warn","message":"UI build directory not found, skipping static file serving","service":"fleet-manager","timestamp":"2025-06-05T23:06:31.096Z"}
{"level":"info","message":"Dashboard server listening on localhost:3000","service":"fleet-manager","timestamp":"2025-06-05T23:06:31.128Z"}
{"level":"info","message":"Dashboard Server initialized successfully","service":"fleet-manager","timestamp":"2025-06-05T23:06:31.129Z"}
{"level":"info","message":"Dashboard server started successfully","service":"fleet-manager","timestamp":"2025-06-05T23:06:31.129Z"}
{"level":"info","message":"Initializing fleet controller...","service":"fleet-manager","timestamp":"2025-06-05T23:06:31.129Z"}
{"level":"info","message":"Initializing Fleet Controller","service":"fleet-manager","timestamp":"2025-06-05T23:06:31.130Z"}
{"level":"info","message":"Distributed mode: false, Role: master","service":"fleet-manager","timestamp":"2025-06-05T23:06:31.130Z"}
{"level":"info","message":"Performing standard Fleet Controller initialization.","service":"fleet-manager","timestamp":"2025-06-05T23:06:31.130Z"}
{"count":0,"level":"info","message":"Loaded existing agents","service":"fleet-manager","timestamp":"2025-06-05T23:06:31.134Z"}
{"level":"info","maxAgents":10,"message":"Fleet Controller initialized successfully","minAgents":7,"service":"fleet-manager","targetUtilization":0.8,"timestamp":"2025-06-05T23:06:31.134Z"}
{"level":"info","message":"Fleet controller initialized successfully","service":"fleet-manager","timestamp":"2025-06-05T23:06:31.135Z"}
{"dashboardUrl":"http://localhost:3000","level":"info","maxAgents":10,"message":"Fleet Management System started successfully!","service":"fleet-manager","timestamp":"2025-06-05T23:06:31.136Z"}
