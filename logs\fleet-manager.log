{"level":"info","message":"Database initialized","path":"D:\\TW\\data\\fleet.db","service":"fleet-manager","timestamp":"2025-06-05T23:01:27.829Z"}
{"activeScaleParams":{},"level":"info","message":"FleetController initializing with scale level: \"medium\"","service":"fleet-manager","sourceScaleLevelsConfig":{},"timestamp":"2025-06-05T23:01:27.992Z"}
{"currentScaleLevelName":"medium","level":"info","maxAgents":10,"message":"FleetController effective scaling parameters:","minAgents":7,"scaleDownBatchSize":3,"scaleUpBatchSize":5,"service":"fleet-manager","targetUtilization":0.8,"timestamp":"2025-06-05T23:01:27.993Z"}
{"isDevelopment":true,"level":"info","maxAgents":10,"message":"Fleet Manager App initializing","nodeEnv":"development","service":"fleet-manager","timestamp":"2025-06-05T23:01:28.342Z"}
{"level":"info","message":"Starting Twitch Fleet Management System...","service":"fleet-manager","timestamp":"2025-06-05T23:01:28.343Z"}
{"level":"info","message":"Initializing database...","service":"fleet-manager","timestamp":"2025-06-05T23:01:28.344Z"}
{"level":"info","message":"Database tables created successfully","service":"fleet-manager","timestamp":"2025-06-05T23:01:28.363Z"}
{"level":"info","message":"Database initialized successfully","service":"fleet-manager","timestamp":"2025-06-05T23:01:28.366Z"}
{"level":"info","message":"Initializing dashboard server...","service":"fleet-manager","timestamp":"2025-06-05T23:01:28.368Z"}
{"level":"info","message":"Initializing Dashboard Server","service":"fleet-manager","timestamp":"2025-06-05T23:01:28.370Z"}
{"level":"warn","message":"UI build directory not found, skipping static file serving","service":"fleet-manager","timestamp":"2025-06-05T23:01:28.373Z"}
{"level":"info","message":"Dashboard server listening on localhost:3000","service":"fleet-manager","timestamp":"2025-06-05T23:01:28.411Z"}
{"level":"info","message":"Dashboard Server initialized successfully","service":"fleet-manager","timestamp":"2025-06-05T23:01:28.411Z"}
{"level":"info","message":"Dashboard server started successfully","service":"fleet-manager","timestamp":"2025-06-05T23:01:28.412Z"}
{"level":"info","message":"Initializing fleet controller...","service":"fleet-manager","timestamp":"2025-06-05T23:01:28.412Z"}
{"level":"info","message":"Initializing Fleet Controller","service":"fleet-manager","timestamp":"2025-06-05T23:01:28.412Z"}
{"level":"info","message":"Distributed mode: false, Role: master","service":"fleet-manager","timestamp":"2025-06-05T23:01:28.412Z"}
{"level":"info","message":"Performing standard Fleet Controller initialization.","service":"fleet-manager","timestamp":"2025-06-05T23:01:28.413Z"}
{"count":0,"level":"info","message":"Loaded existing agents","service":"fleet-manager","timestamp":"2025-06-05T23:01:28.420Z"}
{"level":"info","maxAgents":10,"message":"Fleet Controller initialized successfully","minAgents":7,"service":"fleet-manager","targetUtilization":0.8,"timestamp":"2025-06-05T23:01:28.420Z"}
{"level":"info","message":"Fleet controller initialized successfully","service":"fleet-manager","timestamp":"2025-06-05T23:01:28.421Z"}
{"dashboardUrl":"http://localhost:3000","level":"info","maxAgents":10,"message":"Fleet Management System started successfully!","service":"fleet-manager","timestamp":"2025-06-05T23:01:28.421Z"}
{"channel":"metrics","error":"ResourceMonitor.getCurrentMetrics is not a function","level":"error","message":"Failed to broadcast updates","service":"fleet-manager","timestamp":"2025-06-05T23:01:29.421Z"}
{"channel":"metrics","error":"ResourceMonitor.getCurrentMetrics is not a function","level":"error","message":"Failed to broadcast updates","service":"fleet-manager","timestamp":"2025-06-05T23:01:30.434Z"}
{"channel":"metrics","error":"ResourceMonitor.getCurrentMetrics is not a function","level":"error","message":"Failed to broadcast updates","service":"fleet-manager","timestamp":"2025-06-05T23:01:31.445Z"}
{"channel":"metrics","error":"ResourceMonitor.getCurrentMetrics is not a function","level":"error","message":"Failed to broadcast updates","service":"fleet-manager","timestamp":"2025-06-05T23:01:32.444Z"}
{"channel":"agents","error":"AgentManager.getAgents is not a function","level":"error","message":"Failed to broadcast updates","service":"fleet-manager","timestamp":"2025-06-05T23:01:33.422Z"}
{"level":"info","message":"Starting agent creation process...","service":"fleet-manager","timestamp":"2025-06-05T23:01:33.422Z"}
{"level":"info","message":"Creating agent 1/5...","service":"fleet-manager","timestamp":"2025-06-05T23:01:33.423Z"}
{"level":"info","message":"Node master-node (Role: master) creating agent agent-1749164493423-36hu0 locally.","service":"fleet-manager","timestamp":"2025-06-05T23:01:33.423Z"}
{"error":"Failed to create agent","level":"error","message":"Failed to create agent agent-1749164493423-36hu0 locally on node master-node","service":"fleet-manager","timestamp":"2025-06-05T23:01:33.426Z"}
{"error":"Failed to create agent agent-1749164493423-36hu0 locally on master-node: Failed to create agent","level":"error","message":"Error during agent creation","service":"fleet-manager","timestamp":"2025-06-05T23:01:33.427Z"}
{"channel":"metrics","error":"ResourceMonitor.getCurrentMetrics is not a function","level":"error","message":"Failed to broadcast updates","service":"fleet-manager","timestamp":"2025-06-05T23:01:33.454Z"}
{"channel":"metrics","error":"ResourceMonitor.getCurrentMetrics is not a function","level":"error","message":"Failed to broadcast updates","service":"fleet-manager","timestamp":"2025-06-05T23:01:34.465Z"}
{"channel":"metrics","error":"ResourceMonitor.getCurrentMetrics is not a function","level":"error","message":"Failed to broadcast updates","service":"fleet-manager","timestamp":"2025-06-05T23:01:35.471Z"}
{"channel":"metrics","error":"ResourceMonitor.getCurrentMetrics is not a function","level":"error","message":"Failed to broadcast updates","service":"fleet-manager","timestamp":"2025-06-05T23:01:36.470Z"}
{"channel":"metrics","error":"ResourceMonitor.getCurrentMetrics is not a function","level":"error","message":"Failed to broadcast updates","service":"fleet-manager","timestamp":"2025-06-05T23:01:37.486Z"}
{"channel":"streams","error":"StreamManager.getStreams is not a function","level":"error","message":"Failed to broadcast updates","service":"fleet-manager","timestamp":"2025-06-05T23:01:38.421Z"}
{"channel":"agents","error":"AgentManager.getAgents is not a function","level":"error","message":"Failed to broadcast updates","service":"fleet-manager","timestamp":"2025-06-05T23:01:38.426Z"}
{"channel":"metrics","error":"ResourceMonitor.getCurrentMetrics is not a function","level":"error","message":"Failed to broadcast updates","service":"fleet-manager","timestamp":"2025-06-05T23:01:38.506Z"}
{"channel":"metrics","error":"ResourceMonitor.getCurrentMetrics is not a function","level":"error","message":"Failed to broadcast updates","service":"fleet-manager","timestamp":"2025-06-05T23:01:39.506Z"}
{"channel":"metrics","error":"ResourceMonitor.getCurrentMetrics is not a function","level":"error","message":"Failed to broadcast updates","service":"fleet-manager","timestamp":"2025-06-05T23:01:40.516Z"}
{"channel":"metrics","error":"ResourceMonitor.getCurrentMetrics is not a function","level":"error","message":"Failed to broadcast updates","service":"fleet-manager","timestamp":"2025-06-05T23:01:41.526Z"}
{"channel":"metrics","error":"ResourceMonitor.getCurrentMetrics is not a function","level":"error","message":"Failed to broadcast updates","service":"fleet-manager","timestamp":"2025-06-05T23:01:42.536Z"}
{"channel":"agents","error":"AgentManager.getAgents is not a function","level":"error","message":"Failed to broadcast updates","service":"fleet-manager","timestamp":"2025-06-05T23:01:43.434Z"}
{"channel":"metrics","error":"ResourceMonitor.getCurrentMetrics is not a function","level":"error","message":"Failed to broadcast updates","service":"fleet-manager","timestamp":"2025-06-05T23:01:43.548Z"}
{"channel":"metrics","error":"ResourceMonitor.getCurrentMetrics is not a function","level":"error","message":"Failed to broadcast updates","service":"fleet-manager","timestamp":"2025-06-05T23:01:44.558Z"}
