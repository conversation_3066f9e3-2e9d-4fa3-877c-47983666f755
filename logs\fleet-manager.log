{"level":"info","message":"Database initialized","path":"D:\\TW\\data\\fleet.db","service":"fleet-manager","timestamp":"2025-06-05T14:22:31.372Z"}
{"activeScaleParams":{},"level":"info","message":"FleetController initializing with scale level: \"medium\"","service":"fleet-manager","sourceScaleLevelsConfig":{},"timestamp":"2025-06-05T14:22:31.499Z"}
{"currentScaleLevelName":"medium","level":"info","maxAgents":10,"message":"FleetController effective scaling parameters:","minAgents":7,"scaleDownBatchSize":3,"scaleUpBatchSize":5,"service":"fleet-manager","targetUtilization":0.8,"timestamp":"2025-06-05T14:22:31.500Z"}
{"isDevelopment":true,"level":"info","maxAgents":10,"message":"Fleet Manager App initializing","nodeEnv":"development","service":"fleet-manager","timestamp":"2025-06-05T14:22:31.784Z"}
{"level":"info","message":"Starting Twitch Fleet Management System...","service":"fleet-manager","timestamp":"2025-06-05T14:22:31.785Z"}
{"level":"info","message":"Initializing database...","service":"fleet-manager","timestamp":"2025-06-05T14:22:31.786Z"}
{"level":"info","message":"Database tables created successfully","service":"fleet-manager","timestamp":"2025-06-05T14:22:31.802Z"}
