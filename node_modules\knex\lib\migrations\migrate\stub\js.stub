/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
  <% if (d.tableName) { %>
  return knex.schema.createTable("<%= d.tableName %>", function(t) {
    t.increments();
    t.timestamp();
  });
  <% } %>
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
  <% if (d.tableName) { %>
  return knex.schema.dropTable("<%= d.tableName %>");
  <% } %>
};
