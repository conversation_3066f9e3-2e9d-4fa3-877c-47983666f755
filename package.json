{"name": "twitch-fleet-manager", "version": "1.0.0", "description": "Twitch RPG Fleet Management System", "main": "src/main.js", "scripts": {"test": "jest", "start": "electron .", "start-fleet": "node fleet-manager.js", "start-fleet-dev": "cross-env NODE_ENV=development node fleet-manager.js", "setup-db": "node src/database/init.js"}, "dependencies": {"@google/generative-ai": "^0.1.3", "@types/jest": "^29.5.12", "axios": "^1.9.0", "cross-env": "^7.0.3", "dotenv": "^16.5.0", "electron": "^28.0.0", "electron-store": "^8.1.0", "express": "^5.1.0", "ioredis": "^5.3.2", "jest": "^29.7.0", "knex": "^3.1.0", "openai": "^5.1.0", "sqlite3": "^5.1.7", "uuid": "^11.1.0", "winston": "^3.17.0", "ws": "^8.18.2"}}