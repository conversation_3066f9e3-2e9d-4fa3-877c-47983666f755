const { BrowserWindow } = require('electron');
const DatabaseManager = require('../database/DatabaseManager');
const Logger = require('../utils/Logger');
const ErrorHandler = require('../utils/ErrorHandler');

class AgentManager {
  constructor() {
    this.activeWindows = new Map();
    this.maxConcurrentWindows = parseInt(process.env.MAX_CONCURRENT_WINDOWS || '50');
    this.windowWidth = parseInt(process.env.WINDOW_WIDTH || '1280');
    this.windowHeight = parseInt(process.env.WINDOW_HEIGHT || '720');
    this.userDataDir = process.env.USER_DATA_DIR || './user-data';
  }

  async createAgentWindow(agentId) {
    if (this.activeWindows.size >= this.maxConcurrentWindows) {
      throw ErrorHandler.createCustomError(
        'AgentManagerError',
        'Maximum concurrent windows limit reached',
        { currentCount: this.activeWindows.size, maxWindows: this.maxConcurrentWindows }
      );
    }

    try {
      const window = new BrowserWindow({
        width: this.windowWidth,
        height: this.windowHeight,
        show: false, // Hide window by default
        webPreferences: {
          nodeIntegration: false,
          contextIsolation: true,
          partition: `persist:agent-${agentId}` // Separate session storage
        }
      });

      // Load Twitch login page
      await window.loadURL('https://www.twitch.tv/login');

      this.activeWindows.set(agentId, {
        window,
        createdAt: new Date(),
        status: 'initializing'
      });

      // Set up event handlers
      this.setupWindowEventHandlers(agentId, window);

      Logger.info('Agent window created', { agentId });
      return window;
    } catch (error) {
      throw ErrorHandler.createCustomError(
        'AgentManagerError',
        'Failed to create agent window',
        { agentId, originalError: error.message }
      );
    }
  }

  setupWindowEventHandlers(agentId, window) {
    window.webContents.on('did-finish-load', () => {
      Logger.debug('Page loaded', { agentId, url: window.webContents.getURL() });
    });

    window.webContents.on('did-fail-load', (event, errorCode, errorDescription) => {
      Logger.error('Page failed to load', { agentId, errorCode, errorDescription });
    });

    window.on('closed', () => {
      this.handleWindowClosed(agentId);
    });
  }

  async navigateToStream(agentId, streamUrl) {
    const windowData = this.activeWindows.get(agentId);
    if (!windowData) {
      throw ErrorHandler.createCustomError(
        'AgentManagerError',
        'Agent window not found',
        { agentId }
      );
    }

    try {
      await windowData.window.loadURL(streamUrl);
      windowData.status = 'watching';
      Logger.info('Agent navigated to stream', { agentId, streamUrl });
    } catch (error) {
      throw ErrorHandler.createCustomError(
        'AgentManagerError',
        'Failed to navigate to stream',
        { agentId, streamUrl, originalError: error.message }
      );
    }
  }

  async captureStreamContent(agentId) {
    const windowData = this.activeWindows.get(agentId);
    if (!windowData || windowData.status !== 'watching') {
      throw ErrorHandler.createCustomError(
        'AgentManagerError',
        'Agent not in watching state',
        { agentId }
      );
    }

    try {
      const page = windowData.window.webContents;
      
      // Capture screenshot
      const screenshot = await page.capturePage();
      
      // Get chat messages
      const chatMessages = await page.executeJavaScript(`
        Array.from(document.querySelectorAll('.chat-line__message')).map(msg => ({
          username: msg.querySelector('.chat-author__display-name')?.textContent,
          message: msg.querySelector('.text-fragment')?.textContent,
          timestamp: msg.getAttribute('data-timestamp')
        }));
      `);

      // Get stream info
      const streamInfo = await page.executeJavaScript(`
        ({
          title: document.querySelector('.stream-title')?.textContent,
          viewerCount: document.querySelector('.tw-channel-status-text-viewer-count')?.textContent,
          category: document.querySelector('.game-hover')?.textContent
        });
      `);

      return {
        screenshot: screenshot.toDataURL(),
        chatMessages,
        streamInfo
      };
    } catch (error) {
      throw ErrorHandler.createCustomError(
        'AgentManagerError',
        'Failed to capture stream content',
        { agentId, originalError: error.message }
      );
    }
  }

  async sendChatMessage(agentId, message) {
    const windowData = this.activeWindows.get(agentId);
    if (!windowData || windowData.status !== 'watching') {
      throw ErrorHandler.createCustomError(
        'AgentManagerError',
        'Agent not in watching state',
        { agentId }
      );
    }

    try {
      await windowData.window.webContents.executeJavaScript(`
        const chatInput = document.querySelector('textarea[data-a-target="chat-input"]');
        const chatButton = document.querySelector('button[data-a-target="chat-send-button"]');
        
        if (chatInput && chatButton) {
          chatInput.value = ${JSON.stringify(message)};
          chatInput.dispatchEvent(new Event('input', { bubbles: true }));
          chatButton.click();
          true;
        } else {
          throw new Error('Chat input elements not found');
        }
      `);

      Logger.info('Chat message sent', { agentId, message });
    } catch (error) {
      throw ErrorHandler.createCustomError(
        'AgentManagerError',
        'Failed to send chat message',
        { agentId, message, originalError: error.message }
      );
    }
  }

  handleWindowClosed(agentId) {
    const windowData = this.activeWindows.get(agentId);
    if (windowData) {
      this.activeWindows.delete(agentId);
      Logger.info('Agent window closed', { agentId });
    }
  }

  async destroyAgentWindow(agentId) {
    const windowData = this.activeWindows.get(agentId);
    if (!windowData) {
      return; // Window already destroyed or doesn't exist
    }

    try {
      windowData.window.destroy();
      this.activeWindows.delete(agentId);
      Logger.info('Agent window destroyed', { agentId });
    } catch (error) {
      throw ErrorHandler.createCustomError(
        'AgentManagerError',
        'Failed to destroy agent window',
        { agentId, originalError: error.message }
      );
    }
  }

  getAgentStatus(agentId) {
    const windowData = this.activeWindows.get(agentId);
    return windowData ? {
      status: windowData.status,
      createdAt: windowData.createdAt,
      url: windowData.window.webContents.getURL()
    } : null;
  }

  cleanup() {
    for (const [agentId, windowData] of this.activeWindows) {
      try {
        windowData.window.destroy();
      } catch (error) {
        Logger.error('Failed to cleanup agent window', { agentId, error });
      }
    }
    this.activeWindows.clear();
    Logger.info('AgentManager cleanup completed');
  }
}

module.exports = new AgentManager(); 