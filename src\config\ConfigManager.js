const path = require('path');
const fs = require('fs');
const Store = require('electron-store');
const dotenv = require('dotenv');

// Define default constants for configuration values
const DEFAULT_MAX_AGENTS = 100;
const DEFAULT_MIN_AGENTS_FACTOR = 0.7; // Factor to calculate minAgents if not set
const DEFAULT_TARGET_UTILIZATION = 0.8;
const DEFAULT_HEALTH_CHECK_INTERVAL = 60000;
const DEFAULT_SCALE_UP_BATCH_SIZE = 5;
const DEFAULT_SCALE_DOWN_BATCH_SIZE = 3;
const DEFAULT_AGENT_LAUNCH_INTERVAL = 5000;
const DEFAULT_DASHBOARD_PORT = 3000;
const DEFAULT_DATABASE_PATH = './db/fleet-manager.db';
const DEFAULT_ACCOUNTS_PATH = './config/accounts.json';
const DEFAULT_STREAMERS_PATH = './config/rpg_streamers.json';
const DEFAULT_LOG_PATH = './logs';
const DEFAULT_MESSAGE_MIN_INTERVAL = 300000;
const DEFAULT_MESSAGE_RANDOMIZATION = 0.5;
const DEFAULT_CATEGORY_CHECK_INTERVAL = 300000;
const DEFAULT_MAX_RETRIES = 3;
const DEFAULT_RETRY_BACKOFF_BASE = 5000; // Renamed from retryDelay for clarity
const DEFAULT_MAX_SAME_STREAM_CHAT_PERCENTAGE = 0.3;
const DEFAULT_MAX_MESSAGES_PER_HOUR = 3;
const DEFAULT_BROWSER_WIDTH = 1280;
const DEFAULT_BROWSER_HEIGHT = 720;
const DEFAULT_NODE_ENV = 'production';
const DEFAULT_DEBUG = false;
const DEFAULT_SAVE_SCREENSHOTS = false;
const DEFAULT_DISTRIBUTED_ENABLED = false;
const DEFAULT_NODE_ROLE = 'master';
const DEFAULT_MASTER_NODE_URL = 'http://localhost:12345';
const DEFAULT_NODE_PORT = 12345;
const DEFAULT_NODE_ID = 'default-node-id'; // Generic default
const DEFAULT_INITIAL_SCALE_LEVEL = 'medium';

/**
 * Configuration manager for the fleet management system
 */
class ConfigManager {
  constructor(options = {}) {
    this.store = new Store();
    this.envPath = path.join(process.cwd(), '.env');
    this.loadEnvFile(); // Load .env first to make process.env available

    this.environment = options.environment || process.env.NODE_ENV || DEFAULT_NODE_ENV;
    this.maxAgents = parseInt(process.env.MAX_AGENTS, 10) || options.maxAgents || DEFAULT_MAX_AGENTS;
    this.agentLaunchInterval = parseInt(process.env.AGENT_LAUNCH_INTERVAL, 10) || options.agentLaunchInterval || DEFAULT_AGENT_LAUNCH_INTERVAL;
    this.dashboardPort = parseInt(process.env.DASHBOARD_PORT, 10) || options.dashboardPort || DEFAULT_DASHBOARD_PORT;
    this.databasePath = process.env.DATABASE_PATH || options.databasePath || DEFAULT_DATABASE_PATH;

    this.twitchClientId = process.env.TWITCH_CLIENT_ID || options.twitchClientId;
    this.twitchClientSecret = process.env.TWITCH_CLIENT_SECRET || options.twitchClientSecret;
    this.geminiApiKey = process.env.GEMINI_API_KEY || options.geminiApiKey;

    this.accountsPath = process.env.ACCOUNTS_PATH || options.accountsPath || DEFAULT_ACCOUNTS_PATH;
    this.streamersPath = process.env.STREAMERS_PATH || options.streamersPath || DEFAULT_STREAMERS_PATH;
    this.logPath = process.env.LOG_PATH || options.logPath || DEFAULT_LOG_PATH;

    this.messageMinInterval = parseInt(process.env.MESSAGE_MIN_INTERVAL, 10) || options.messageMinInterval || DEFAULT_MESSAGE_MIN_INTERVAL;
    this.messageRandomization = parseFloat(process.env.MESSAGE_RANDOMIZATION) || options.messageRandomization || DEFAULT_MESSAGE_RANDOMIZATION;
    this.categoryCheckInterval = parseInt(process.env.CATEGORY_CHECK_INTERVAL, 10) || options.categoryCheckInterval || DEFAULT_CATEGORY_CHECK_INTERVAL;
    this.maxRetries = parseInt(process.env.MAX_RETRIES, 10) || options.maxRetries || DEFAULT_MAX_RETRIES;
    this.retryBackoffBase = parseInt(process.env.RETRY_BACKOFF_BASE, 10) || options.retryBackoffBase || DEFAULT_RETRY_BACKOFF_BASE;

    this.maxSameStreamChatPercentage = parseFloat(process.env.MAX_SAME_STREAM_CHAT_PERCENTAGE) || options.maxSameStreamChatPercentage || DEFAULT_MAX_SAME_STREAM_CHAT_PERCENTAGE;
    this.maxMessagesPerHour = parseInt(process.env.MAX_MESSAGES_PER_HOUR, 10) || options.maxMessagesPerHour || DEFAULT_MAX_MESSAGES_PER_HOUR;
    this.browserWidth = parseInt(process.env.BROWSER_WIDTH, 10) || options.browserWidth || DEFAULT_BROWSER_WIDTH;
    this.browserHeight = parseInt(process.env.BROWSER_HEIGHT, 10) || options.browserHeight || DEFAULT_BROWSER_HEIGHT;
    
    this.debug = (process.env.DEBUG === 'true') || options.debug || DEFAULT_DEBUG;
    this.saveScreenshots = (process.env.SAVE_SCREENSHOTS === 'true') || options.saveScreenshots || DEFAULT_SAVE_SCREENSHOTS;

    // Distributed Deployment Settings
    this.distributedEnabled = (process.env.DISTRIBUTED_ENABLED === 'true') || options.distributedEnabled || DEFAULT_DISTRIBUTED_ENABLED;
    this.nodeRole = process.env.NODE_ROLE || options.nodeRole || DEFAULT_NODE_ROLE; // 'master' or 'worker'
    this.masterNodeUrl = process.env.MASTER_NODE_URL || options.masterNodeUrl || DEFAULT_MASTER_NODE_URL;
    this.nodePort = parseInt(process.env.NODE_PORT, 10) || options.nodePort || DEFAULT_NODE_PORT;
    this.nodeId = process.env.NODE_ID || options.nodeId || (this.nodeRole === 'master' ? 'master-node' : `worker-node-${Date.now()}`);

    // The existing scaleConfig seems fine, no need to tie it to .env for now unless specified
    this.scaleConfig = {
      small: { level: 'small', maxAgents: 30, cpuThreshold: 70, memoryThreshold: 80 },
      medium: { level: 'medium', maxAgents: 60, cpuThreshold: 75, memoryThreshold: 85 },
      large: { level: 'large', maxAgents: 90, cpuThreshold: 80, memoryThreshold: 90 },
      xlarge: { level: 'xlarge', maxAgents: 120, cpuThreshold: 85, memoryThreshold: 95 }
    };

    // Load any overrides from electron-store, potentially overriding .env or defaults
    this.loadFromStore();

    dotenv.config();
    this.config = {};
    this.loadGlobalConfigFromEnvAndDefaults(options);

    try {
      this.scaleLevelsConfig = JSON.parse(this.getEnv('SCALE_LEVELS_CONFIG', '{}'));
    } catch (error) {
      console.warn('Failed to parse SCALE_LEVELS_CONFIG. Using empty object. Error:', error.message);
      this.scaleLevelsConfig = {};
    }
    this.currentScaleLevelName = this.getEnv('INITIAL_SCALE_LEVEL', DEFAULT_INITIAL_SCALE_LEVEL);
    this.activeScaleConfig = this.scaleLevelsConfig[this.currentScaleLevelName] || {};

    if (Object.keys(this.activeScaleConfig).length === 0 && this.scaleLevelsConfig[this.currentScaleLevelName] === undefined) {
      console.warn(`Initial scale level "${this.currentScaleLevelName}" not found in SCALE_LEVELS_CONFIG or is empty. Scale-specific overrides will not apply.`);
    } else {
      console.log(`ConfigManager initialized with scale level: "${this.currentScaleLevelName}"`, { activeConfig: this.activeScaleConfig });
    }
  }

  loadEnvFile() {
    try {
      if (fs.existsSync(this.envPath)) {
        const envConfig = require('dotenv').parse(fs.readFileSync(this.envPath));
        for (const key in envConfig) {
          if (process.env[key] === undefined) { // Only set if not already set by actual environment
             process.env[key] = envConfig[key];
          }
        }
      }
    } catch (err) {
      console.error('Error loading .env file:', err);
    }
  }

  loadFromStore() {
    const storedConfig = this.store.store;
    for (const key in storedConfig) {
        // Prioritize direct properties, then check if this key matches one of our config properties
        if (this.hasOwnProperty(key)) {
            const storedValue = storedConfig[key];
            // Basic type coercion based on expected types for new distributed configs
            if (key === 'distributedEnabled' || key === 'saveScreenshots' || key === 'debug') {
                this[key] = (storedValue === 'true' || storedValue === true);
            } else if (key === 'nodePort' || key === 'maxAgents' /* add other int parsable here */ || key === 'agentLaunchInterval' || key === 'dashboardPort' || key === 'messageMinInterval' || key === 'categoryCheckInterval' || key === 'maxRetries' || key === 'retryBackoffBase' || key === 'maxMessagesPerHour' || key === 'browserWidth' || key === 'browserHeight') {
                this[key] = parseInt(storedValue, 10);
            } else if (key === 'messageRandomization' /* add other float parsable here */ || key === 'maxSameStreamChatPercentage') {
                this[key] = parseFloat(storedValue);
            } else {
                this[key] = storedValue;
            }
        }
    }
  }

  loadGlobalConfigFromEnvAndDefaults(options = {}) {
    // Helper to get value from options, then env, then default
    const getOptionEnvDefault = (optionKey, envKey, defaultValue) => {
        if (options[optionKey] !== undefined) return options[optionKey];
        return this.getEnv(envKey, defaultValue);
    };
    
    // Load and parse all known global configurations
    this.config.maxAgents = parseInt(getOptionEnvDefault('maxAgents', 'MAX_AGENTS', DEFAULT_MAX_AGENTS), 10);
    let minAgentsDefault = Math.floor(this.config.maxAgents * DEFAULT_MIN_AGENTS_FACTOR);
    this.config.minAgents = parseInt(getOptionEnvDefault('minAgents', 'MIN_AGENTS', minAgentsDefault), 10);
    
    this.config.targetUtilization = parseFloat(getOptionEnvDefault('targetUtilization', 'TARGET_UTILIZATION', DEFAULT_TARGET_UTILIZATION));
    this.config.healthCheckInterval = parseInt(getOptionEnvDefault('healthCheckInterval', 'HEALTH_CHECK_INTERVAL', DEFAULT_HEALTH_CHECK_INTERVAL), 10);
    this.config.scaleUpBatchSize = parseInt(getOptionEnvDefault('scaleUpBatchSize', 'SCALE_UP_BATCH_SIZE', DEFAULT_SCALE_UP_BATCH_SIZE), 10);
    this.config.scaleDownBatchSize = parseInt(getOptionEnvDefault('scaleDownBatchSize', 'SCALE_DOWN_BATCH_SIZE', DEFAULT_SCALE_DOWN_BATCH_SIZE), 10);

    this.config.agentLaunchInterval = parseInt(getOptionEnvDefault('agentLaunchInterval', 'AGENT_LAUNCH_INTERVAL', DEFAULT_AGENT_LAUNCH_INTERVAL), 10);
    this.config.dashboardPort = parseInt(getOptionEnvDefault('dashboardPort', 'DASHBOARD_PORT', DEFAULT_DASHBOARD_PORT), 10);
    this.config.databasePath = getOptionEnvDefault('databasePath', 'DATABASE_PATH', DEFAULT_DATABASE_PATH);
    this.config.accountsPath = getOptionEnvDefault('accountsPath', 'ACCOUNTS_PATH', DEFAULT_ACCOUNTS_PATH);
    this.config.streamersPath = getOptionEnvDefault('streamersPath', 'STREAMERS_PATH', DEFAULT_STREAMERS_PATH);
    this.config.logPath = getOptionEnvDefault('logPath', 'LOG_PATH', DEFAULT_LOG_PATH);
    this.config.messageMinInterval = parseInt(getOptionEnvDefault('messageMinInterval', 'MESSAGE_MIN_INTERVAL', DEFAULT_MESSAGE_MIN_INTERVAL), 10);
    this.config.messageRandomization = parseFloat(getOptionEnvDefault('messageRandomization', 'MESSAGE_RANDOMIZATION', DEFAULT_MESSAGE_RANDOMIZATION));
    this.config.categoryCheckInterval = parseInt(getOptionEnvDefault('categoryCheckInterval', 'CATEGORY_CHECK_INTERVAL', DEFAULT_CATEGORY_CHECK_INTERVAL), 10);
    this.config.maxRetries = parseInt(getOptionEnvDefault('maxRetries', 'MAX_RETRIES', DEFAULT_MAX_RETRIES), 10);
    this.config.retryBackoffBase = parseInt(getOptionEnvDefault('retryBackoffBase', 'RETRY_BACKOFF_BASE', DEFAULT_RETRY_BACKOFF_BASE), 10);
    this.config.maxSameStreamChatPercentage = parseFloat(getOptionEnvDefault('maxSameStreamChatPercentage', 'MAX_SAME_STREAM_CHAT_PERCENTAGE', DEFAULT_MAX_SAME_STREAM_CHAT_PERCENTAGE));
    this.config.maxMessagesPerHour = parseInt(getOptionEnvDefault('maxMessagesPerHour', 'MAX_MESSAGES_PER_HOUR', DEFAULT_MAX_MESSAGES_PER_HOUR), 10);
    this.config.browserWidth = parseInt(getOptionEnvDefault('browserWidth', 'BROWSER_WIDTH', DEFAULT_BROWSER_WIDTH), 10);
    this.config.browserHeight = parseInt(getOptionEnvDefault('browserHeight', 'BROWSER_HEIGHT', DEFAULT_BROWSER_HEIGHT), 10);
    this.config.environment = getOptionEnvDefault('environment', 'NODE_ENV', DEFAULT_NODE_ENV);
    this.config.debug = getOptionEnvDefault('debug', 'DEBUG', DEFAULT_DEBUG).toString().toLowerCase() === 'true';
    this.config.saveScreenshots = getOptionEnvDefault('saveScreenshots', 'SAVE_SCREENSHOTS', DEFAULT_SAVE_SCREENSHOTS).toString().toLowerCase() === 'true';

    this.config.distributedEnabled = getOptionEnvDefault('distributedEnabled', 'DISTRIBUTED_ENABLED', DEFAULT_DISTRIBUTED_ENABLED).toString().toLowerCase() === 'true';
    this.config.nodeRole = getOptionEnvDefault('nodeRole', 'NODE_ROLE', DEFAULT_NODE_ROLE);
    this.config.masterNodeUrl = getOptionEnvDefault('masterNodeUrl', 'MASTER_NODE_URL', DEFAULT_MASTER_NODE_URL);
    this.config.nodePort = parseInt(getOptionEnvDefault('nodePort', 'NODE_PORT', DEFAULT_NODE_PORT), 10);
    this.config.nodeId = getOptionEnvDefault('nodeId', 'NODE_ID', (this.config.nodeRole === 'master' ? 'master-node' : `worker-node-${Date.now()}`));
    
    // Credentials - these should not have direct defaults in code
    this.config.twitchClientId = getOptionEnvDefault('twitchClientId', 'TWITCH_CLIENT_ID', undefined);
    this.config.twitchClientSecret = getOptionEnvDefault('twitchClientSecret', 'TWITCH_CLIENT_SECRET', undefined);
    this.config.geminiApiKey = getOptionEnvDefault('geminiApiKey', 'GEMINI_API_KEY', undefined);

    // SCALE_LEVELS_CONFIG and INITIAL_SCALE_LEVEL are handled in constructor directly using getEnv
  }

  getEnv(key, defaultValue) {
    return process.env[key] === undefined ? defaultValue : process.env[key];
  }

  /**
   * Get configuration value
   * @param {string} key - Configuration key
   * @returns {*} Configuration value
   */
  get(key) {
    // 1. Prioritize active scale level configuration for overridable keys
    if (this.activeScaleConfig && typeof this.activeScaleConfig[key] !== 'undefined') {
      return this.activeScaleConfig[key];
    }

    // 2. Fallback to global config (loaded from options, .env, or defaults by loadGlobalConfigFromEnvAndDefaults)
    if (typeof this.config[key] !== 'undefined') {
      return this.config[key];
    }
    
    // Special handling for direct access to processed scale level info
    if (key === 'initialScaleLevel') return this.currentScaleLevelName;
    if (key === 'scaleLevelsConfig') return this.scaleLevelsConfig;

    // Warn if key is not found in primary config sources and isn't a special key
    console.warn(`Config key "${key}" not found in active scale config or global config. Returning undefined.`);
    return undefined;
  }

  /**
   * Set configuration value. Persists to electron-store.
   * @param {string} key - Configuration key
   * @param {*} value - Configuration value
   */
  set(key, value) {
    this[key] = value; // Update in-memory cache
    this.store.set(key, value); // Persist to electron-store
  }

  /**
   * Get scale configuration for a specific level
   * @param {string} level - Scale level (small, medium, large, xlarge)
   * @returns {Object} Scale configuration
   */
  getScaleConfig(level = 'small') {
    return this.scaleConfig[level];
  }

  getRequired(key) {
    const value = this.get(key);
    if (value === null || value === undefined || value === '') { // Check for empty string for critical keys
      throw new Error(`Required configuration key "${key}" is not set or is empty`);
    }
    return value;
  }

  // Get all configuration as an object
  getAll() {
    return {
        ...this.config,
        ...this.activeScaleConfig, // Overrides from active scale level
        currentScaleLevelName: this.currentScaleLevelName, // Add current level name
        // scaleLevelsConfig: this.scaleLevelsConfig, // Optionally include all defined levels
    };
  }

  // Validate required configuration
  validate() {
    const requiredKeys = [
      'twitchClientId',
      'twitchClientSecret',
      'geminiApiKey'
    ];

    const missing = requiredKeys.filter(key => {
        const value = this.get(key);
        return value === null || value === undefined || value === ''; // Also check for empty string for these critical keys
    });

    if (missing.length > 0) {
      throw new Error(`Missing or empty required configuration: ${missing.join(', ')}`);
    }
    console.log("Core configuration validated successfully.");
  }

  // Clear all stored configuration in electron-store and reset in-memory
  clear() {
    this.store.clear();
    // Re-initialize to defaults by creating a new temporary instance
    // and copying its properties, or re-run constructor logic carefully.
    // For simplicity, let's just clear the store. A full reset would re-run constructor.
    // Caller might need to re-instantiate ConfigManager for a full reset to defaults if desired.
    console.log("Electron-store cleared. In-memory config might still hold values until next instantiation with no overrides.");
  }

  // Method to get a specific scale level's configuration object
  getScaleLevelConfig(levelName) {
    return this.scaleLevelsConfig[levelName] || null;
  }

  // Method to get the name of the currently active scale level
  getCurrentScaleLevelName() {
    return this.currentScaleLevelName;
  }
}

module.exports = ConfigManager; 