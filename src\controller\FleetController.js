const DatabaseManager = require('../database/DatabaseManager');
const Logger = require('../utils/Logger');
const ErrorHandler = require('../utils/ErrorHandler');
const ConfigManager = require('../config/ConfigManager');

class FleetController {
  constructor() {
    this.config = new ConfigManager(); // Use an instance

    // Determine active scale level and its configuration
    this.currentScaleLevelName = this.config.getCurrentScaleLevelName();
    this.activeScaleConfig = this.config.getScaleLevelConfig(this.currentScaleLevelName) || {};

    Logger.info(
      `FleetController initializing with scale level: "${this.currentScaleLevelName}"`, 
      { 
        activeScaleParams: this.activeScaleConfig, 
        sourceScaleLevelsConfig: this.config.get('scaleLevelsConfig') 
      }
    );

    // Initialize scaling parameters, prioritizing activeScaleConfig, then global defaults
    this.maxAgents = typeof this.activeScaleConfig.maxAgents !== 'undefined' 
        ? this.activeScaleConfig.maxAgents 
        : this.config.get('maxAgents');
    this.minAgents = typeof this.activeScaleConfig.minAgents !== 'undefined' 
        ? this.activeScaleConfig.minAgents 
        : this.config.get('minAgents');
    this.targetUtilization = typeof this.activeScaleConfig.targetUtilization !== 'undefined' 
        ? this.activeScaleConfig.targetUtilization 
        : this.config.get('targetUtilization');
    this.scaleUpBatchSize = typeof this.activeScaleConfig.scaleUpBatchSize !== 'undefined' 
        ? this.activeScaleConfig.scaleUpBatchSize 
        : this.config.get('scaleUpBatchSize');
    this.scaleDownBatchSize = typeof this.activeScaleConfig.scaleDownBatchSize !== 'undefined' 
        ? this.activeScaleConfig.scaleDownBatchSize 
        : this.config.get('scaleDownBatchSize');

    // Other configurations that are not typically per-scale-level
    this.healthCheckInterval = this.config.get('healthCheckInterval');
    this.initialized = false;
    this.maxRetries = this.config.get('maxRetries');
    this.retryDelay = this.config.get('retryBackoffBase'); // Note: key changed in ConfigManager defaults
    this.errorThreshold = this.config.get('errorThreshold');
    this.recoveryInterval = this.config.get('recoveryInterval'); 
    this.errorCounts = new Map();

    // Distributed deployment settings (not typically per-scale-level, but could be if needed)
    this.distributedEnabled = this.config.get('distributedEnabled');
    this.nodeRole = this.config.get('nodeRole');
    this.masterNodeUrl = this.config.get('masterNodeUrl');
    this.nodePort = this.config.get('nodePort');
    this.nodeId = this.config.get('nodeId');

    this.agents = new Map(); // Local agents for this node
    this.workerNodes = new Map(); 
    this.agentToWorkerMap = new Map();

    Logger.info('FleetController effective scaling parameters:', {
        maxAgents: this.maxAgents,
        minAgents: this.minAgents,
        targetUtilization: this.targetUtilization,
        scaleUpBatchSize: this.scaleUpBatchSize,
        scaleDownBatchSize: this.scaleDownBatchSize,
        currentScaleLevelName: this.currentScaleLevelName
    });
    // TODO: Implement a method to dynamically change scale level, e.g., reloadAndApplyScaleLevel(newLevelName)
    // This would involve re-fetching from ConfigManager and updating the above parameters.
  }

  async initialize() {
    try {
      Logger.info('Initializing Fleet Controller');
      Logger.info(`Distributed mode: ${this.distributedEnabled}, Role: ${this.nodeRole}`);

      if (this.distributedEnabled) {
        if (this.nodeRole === 'master') {
          Logger.info('Initializing as MASTER node.');
          await this.initializeMasterNode();
          // Master node also performs standard initialization for its own managed agents (if any) or global state.
        } else if (this.nodeRole === 'worker') {
          Logger.info(`Initializing as WORKER node. Master URL: ${this.masterNodeUrl}`);
          await this.initializeWorkerNode();
          // Worker node initialization might be different, e.g., no global health checks or scaling decisions.
          // It will primarily manage agents assigned by the master.
          // Worker also needs to start its own health monitoring for its *local* agents
          this.startHealthMonitoring(); // Worker monitors its own agents
          this.initialized = true;
          Logger.info('Worker Fleet Controller initialized.');
          return; // Worker might not run the full standard initialization below immediately.
        } else {
          Logger.warn(`Invalid NODE_ROLE: ${this.nodeRole}. Running in standalone mode.`);
          this.distributedEnabled = false; // Fallback to non-distributed
        }
      }

      // Standard initialization for standalone or master node
      Logger.info('Performing standard Fleet Controller initialization.');
      
      // Load existing agents from database
      await this.loadExistingAgents();
      
      // Start health monitoring
      this.startHealthMonitoring();
      
      this.initialized = true;
      Logger.info('Fleet Controller initialized successfully', {
        maxAgents: this.maxAgents,
        minAgents: this.minAgents,
        targetUtilization: this.targetUtilization
      });
    } catch (error) {
      Logger.error('Failed to initialize Fleet Controller', { error });
      throw error;
    }
  }

  async initializeMasterNode() {
    // Placeholder for master node specific initialization
    // e.g., start an HTTP server to listen for worker registrations and commands
    Logger.info(`Master node (${this.nodeId}) specific initialization started.`);
    // TODO: For robust shared state management, workerNodes and agentToWorkerMap should be persisted (e.g., in a database)
    //       and loaded here if the master node restarts. Currently, they are in-memory only.
    Logger.info(`Master node (${this.nodeId}): Initializing in-memory workerNodes and agentToWorkerMap for shared state tracking.`);
    this.workerNodes.clear();
    this.agentToWorkerMap.clear();
    Logger.info(`Master node (${this.nodeId}) ready to accept worker registrations and manage the fleet's shared state.`);
    // TODO: Implement HTTP server for worker communication (e.g., Express app on this.nodePort if master uses it for this)
    // Example endpoint: POST /register-worker { workerId, address, port, capacity }
    // Example endpoint: POST /worker-heartbeat { workerId, currentLoad, status }
    Logger.info(`Master node (${this.nodeId}) specific initialization complete.`);
  }

  // Method for master to handle worker registration
  handleWorkerRegistration(workerId, workerInfo) {
    if (this.nodeRole !== 'master') return;
    Logger.info(`Master node (${this.nodeId}) received registration from worker: ${workerId}. Updating shared state for worker nodes.`, { workerInfo });
    this.workerNodes.set(workerId, {
      ...workerInfo, // e.g., { address, port, capacity }
      currentLoad: 0,
      lastSeen: Date.now(),
      status: 'registered'
    });
  }

  // Placeholder for master to handle worker heartbeat
  handleWorkerHeartbeat(workerId, heartbeatData) {
    if (this.nodeRole !== 'master') return;
    if (!this.workerNodes.has(workerId)) {
      Logger.warn(`Master node (${this.nodeId}) received heartbeat from unknown worker: ${workerId}`);
      // Optionally, could trigger re-registration or ignore
      return;
    }
    // TODO: (Master Node Resource Optimization) Heartbeat data should ideally include worker's resource usage
    // e.g., heartbeatData: { currentLoad, status, cpuUsage, memoryUsage, diskUsage, networkIO }
    // The master would then store this on the workerNode entry for more informed agent distribution
    // and overall fleet health monitoring.
    // For now, we just log that we expect it.
    Logger.info(
        `Master node (${this.nodeId}) received heartbeat from worker: ${workerId}. Updating shared worker state. Heartbeat should include resource metrics.`, 
        { heartbeatData }
    );
    this.workerNodes.set(workerId, {
      ...this.workerNodes.get(workerId),
      ...heartbeatData, // e.g., { currentLoad, status }
      lastSeen: Date.now()
    });
    Logger.debug(`Master node (${this.nodeId}) received heartbeat from worker: ${workerId}`, { heartbeatData });
  }

  async initializeWorkerNode() {
    // Placeholder for worker node specific initialization
    // e.g., register with the master node, setup communication channel
    Logger.info(`Worker node (${this.nodeId}) specific initialization started.`);
    try {
      // Simulate registration details that the worker would send to the master.
      const registrationDetails = {
        workerId: this.nodeId,
        address: `http://worker-address-placeholder:${this.nodePort}`, // Worker needs to know its own reachable address
        port: this.nodePort, // Port worker listens on for commands from master
        capacity: this.config.get('maxAgents'), // Worker's own capacity for agents
        // TODO: Add other relevant state for master, e.g., current OS, resource availability initial snapshot
      };
      Logger.info(`Worker node (${this.nodeId}) registration details (to be shared with master):`, registrationDetails);
      Logger.info(`Worker node (${this.nodeId}) would now attempt to call master.handleWorkerRegistration to share its initial state.`);

      // Simulate sending periodic heartbeats to the master.
      // This is conceptual; in a real system, this would be a timer initiating network calls.
      Logger.info(`Worker node (${this.nodeId}) would periodically send heartbeats to the master. This heartbeat is crucial for shared state, and would include at least: { currentLoad: this.agents.size, status: 'active', cpuUsage: 'xx%', memoryUsage: 'yyMB', availableMemory: 'zzMB', diskUsage: 'aa%' }. Master would call its handleWorkerHeartbeat.`);
      
      // TODO: Implement actual network call for registration to master (`this.masterNodeUrl`).
      // TODO: Implement actual network calls for periodic heartbeats to master.
      // TODO: Worker should start an HTTP server (if applicable) to listen for commands from the master on its `this.nodePort`.
      //       e.g., app.listen(this.nodePort, () => Logger.info(`Worker ${this.nodeId} listening on ${this.nodePort} for master commands`));

    } catch (error) {
      Logger.error(`Worker node (${this.nodeId}) failed during its initialization process, potentially during simulated registration or setup.`, { error: error.message, masterUrl: this.masterNodeUrl });
      // Depending on the error, might rethrow or attempt recovery.
      // For now, rethrowing to indicate critical failure during init.
      throw new Error(`Worker (${this.nodeId}) failed to complete specific initialization steps: ${error.message}`);
    }
    Logger.info(`Worker node (${this.nodeId}) specific initialization complete. Ready to receive tasks and manage local agents.`);
  }

  async loadExistingAgents() {
    try {
      const agents = await DatabaseManager.getAgentsByStatus('idle');
      agents.forEach(agent => {
        this.agents.set(agent.id, {
          status: agent.status,
          streamId: agent.stream_id,
          lastUpdated: new Date(agent.updated_at)
        });
      });
      Logger.info('Loaded existing agents', { count: agents.length });
    } catch (error) {
      throw ErrorHandler.createCustomError(
        'FleetControllerError',
        'Failed to load existing agents',
        { originalError: error.message }
      );
    }
  }

  startHealthMonitoring() {
    this.healthCheckTimer = setInterval(async () => {
      try {
        await this.performHealthCheck();
      } catch (error) {
        Logger.error('Health check failed', { error });
      }
    }, this.healthCheckInterval);
  }

  async performHealthCheck() {
    const metrics = {
      totalAgents: this.agents.size,
      activeAgents: 0,
      idleAgents: 0,
      errorAgents: 0
    };

    // TODO: If this is a worker node, it should also check its own system resource utilization here.
    // For example, if CPU > 90%, it might log a warning, temporarily reduce its advertised capacity,
    // or include a 'stressed' status in its next heartbeat to the master for resource optimization.
    // Logger.info(`Worker node (${this.nodeId}) performing local resource check during health check.`);

    for (const [agentId, agent] of this.agents) {
      switch (agent.status) {
        case 'watching':
          metrics.activeAgents++;
          break;
        case 'idle':
          metrics.idleAgents++;
          break;
        case 'error':
          metrics.errorAgents++;
          await this.handleErroredAgent(agentId, agent);
          break;
      }
    }

    // Record metrics
    await DatabaseManager.recordMetric('fleet_health', 1, metrics);
    
    // Check if we need to scale the fleet (Only master should do this, or standalone)
    if (this.nodeRole === 'master' || !this.distributedEnabled) {
      await this.adjustFleetSize(metrics);
    }

    // Run error recovery if needed
    if (metrics.errorAgents > 0) {
      await this.runErrorRecovery(metrics);
    }
  }

  async handleErroredAgent(agentId, agent) {
    const errorCount = this.errorCounts.get(agentId) || 0;
    this.errorCounts.set(agentId, errorCount + 1);

    if (errorCount >= this.maxRetries) {
      Logger.warn('Agent exceeded max retries, destroying', { agentId, errorCount });
      await this.destroyAgent(agentId);
      this.errorCounts.delete(agentId);
    } else {
      await this.retryAgent(agentId, agent);
    }
  }

  async retryAgent(agentId, agent) {
    try {
      Logger.info('Attempting to recover agent', { agentId });
      
      // Log the retry attempt
      await DatabaseManager.logAgentEvent(agentId, 'retry_attempt', {
        retryCount: this.errorCounts.get(agentId),
        previousStatus: agent.status,
        streamId: agent.streamId
      });

      // Reset the agent to idle state
      await this.updateAgentStatus(agentId, 'idle', null);

      // Wait for retry delay
      await new Promise(resolve => setTimeout(resolve, this.retryDelay));

      Logger.info('Agent recovery attempt completed', { agentId });
    } catch (error) {
      Logger.error('Agent recovery attempt failed', {
        agentId,
        error: error.message,
        retryCount: this.errorCounts.get(agentId)
      });
    }
  }

  async runErrorRecovery(metrics) {
    try {
      // If too many agents are in error state, trigger emergency recovery
      if (metrics.errorAgents > this.errorThreshold) {
        Logger.warn('Emergency recovery triggered', {
          errorAgents: metrics.errorAgents,
          threshold: this.errorThreshold
        });

        // Log the emergency recovery event
        await DatabaseManager.logAgentEvent('fleet', 'emergency_recovery', {
          errorAgents: metrics.errorAgents,
          totalAgents: metrics.totalAgents
        });

        // Get all errored agents
        const erroredAgents = Array.from(this.agents.entries())
          .filter(([_, agent]) => agent.status === 'error')
          .map(([agentId]) => agentId);

        // Destroy and recreate errored agents
        for (const agentId of erroredAgents) {
          await this.destroyAgent(agentId);
          await this.createAgent();
        }

        // Clear error counts
        this.errorCounts.clear();

        Logger.info('Emergency recovery completed', {
          agentsRecreated: erroredAgents.length
        });
      }
    } catch (error) {
      Logger.error('Error recovery failed', { error: error.message });
    }
  }

  async adjustFleetSize(metrics) {
    const currentUtilization = metrics.activeAgents / this.maxAgents;
    const utilizationGap = Math.abs(currentUtilization - this.targetUtilization);
    
    // Only scale if the gap is significant (more than 5%)
    // And if this node is responsible for scaling (master or standalone)
    if (utilizationGap < 0.05 || (this.distributedEnabled && this.nodeRole !== 'master')) {
      return;
    }

    // TODO: (Master Node Resource Optimization)
    // Consider overall fleet resource health. If many workers report high resource stress,
    // the master might pause scale-up or even trigger scale-down regardless of utilizationGap.
    // Logger.info(`Master node (${this.nodeId}) considering overall fleet resource health for scaling decisions.`);
    
    if (currentUtilization > this.targetUtilization && metrics.totalAgents < this.maxAgents) {
      await this.scaleUp(metrics);
    } else if (currentUtilization < this.targetUtilization && metrics.totalAgents > this.minAgents) {
      await this.scaleDown(metrics);
    }

    // Log the scaling decision
    await DatabaseManager.recordMetric('fleet_scaling', currentUtilization, {
      totalAgents: metrics.totalAgents,
      activeAgents: metrics.activeAgents,
      targetUtilization: this.targetUtilization
    });
  }

  async scaleUp(metrics) {
    try {
      // Calculate how many agents to add
      const agentsToAdd = Math.min(
        this.scaleUpBatchSize,
        this.maxAgents - metrics.totalAgents
      );

      Logger.info('Scaling up fleet', {
        currentAgents: metrics.totalAgents,
        agentsToAdd,
        maxAgents: this.maxAgents
      });
      Logger.info(`Master node (${this.nodeId}) initiating centralized scale-up. Will delegate agent creation if workers are available.`);

      // Create new agents in parallel
      const creationPromises = Array(agentsToAdd)
        .fill(null)
        .map(() => this.createAgent());

      const newAgentIds = await Promise.all(creationPromises);

      // Log the scale-up event
      await DatabaseManager.logAgentEvent('fleet', 'scale_up', {
        agentsAdded: agentsToAdd,
        newAgentIds,
        totalAgents: metrics.totalAgents + agentsToAdd
      });

      return newAgentIds;
    } catch (error) {
      throw ErrorHandler.createCustomError(
        'FleetControllerError',
        'Failed to scale up fleet',
        { originalError: error.message }
      );
    }
  }

  async scaleDown(metrics) {
    try {
      // Calculate how many agents to remove
      const agentsToRemove = Math.min(
        this.scaleDownBatchSize,
        metrics.totalAgents - this.minAgents
      );

      Logger.info('Scaling down fleet', {
        currentAgents: metrics.totalAgents,
        agentsToRemove,
        minAgents: this.minAgents
      });
      Logger.info(`Master node (${this.nodeId}) initiating centralized scale-down. Will delegate agent destruction if agents are on workers.`);

      // Find idle agents to remove
      const idleAgents = Array.from(this.agents.entries())
        .filter(([_, agent]) => agent.status === 'idle')
        .slice(0, agentsToRemove)
        .map(([agentId]) => agentId);

      // If not enough idle agents, find agents with lowest activity
      if (idleAgents.length < agentsToRemove) {
        const activeAgents = await DatabaseManager.getAgentsByStatus('watching');
        const sortedByActivity = activeAgents
          .sort((a, b) => a.watch_time - b.watch_time)
          .slice(0, agentsToRemove - idleAgents.length)
          .map(agent => agent.id);
        
        idleAgents.push(...sortedByActivity);
      }

      // Destroy agents in parallel
      const destructionPromises = idleAgents.map(agentId => this.destroyAgent(agentId));
      await Promise.all(destructionPromises);

      // Log the scale-down event
      await DatabaseManager.logAgentEvent('fleet', 'scale_down', {
        agentsRemoved: idleAgents.length,
        removedAgentIds: idleAgents,
        totalAgents: metrics.totalAgents - idleAgents.length
      });

      return idleAgents;
    } catch (error) {
      throw ErrorHandler.createCustomError(
        'FleetControllerError',
        'Failed to scale down fleet',
        { originalError: error.message }
      );
    }
  }

  async createAgent() {
    // For non-master nodes (workers or standalone), check against their local maxAgents.
    // Master's ability to create locally (if no workers) is also bound by its maxAgents.
    // The overall fleet capacity is managed by the sum of worker capacities + master's local capacity.
    if (this.agents.size >= this.maxAgents && (!this.distributedEnabled || this.nodeRole !== 'master' || (this.distributedEnabled && this.nodeRole === 'master' && this.workerNodes.size === 0))) {
      Logger.warn(`Node ${this.nodeId} (Role: ${this.nodeRole}) reached its local agent limit of ${this.maxAgents}. Cannot create new agent locally.`);
      throw ErrorHandler.createCustomError(
        'FleetControllerError',
        `Node ${this.nodeId} agent limit reached for local creation`
      );
    }
    
    const agentId = `agent-${Date.now()}-${Math.random().toString(36).substring(2, 7)}`;
    
    if (this.distributedEnabled && this.nodeRole === 'master') {
      const selectedWorker = this.selectWorkerForNewAgent();
      if (selectedWorker) {
        Logger.info(`Master node (${this.nodeId}) selected worker ${selectedWorker.id} (Node: ${selectedWorker.nodeId || 'N/A'}) for new agent ${agentId}.`);
        this.agentToWorkerMap.set(agentId, selectedWorker.id);
        
        try {
          // Master creates a placeholder record for tracking the delegation.
          await DatabaseManager.createAgent(agentId, this.nodeId, { 
            status: 'pending_delegation', 
            assigned_worker_id: selectedWorker.id 
          });
          await DatabaseManager.logAgentEvent(agentId, 'delegation_initiated', { 
            masterNodeId: this.nodeId, 
            workerId: selectedWorker.id,
            workerNodeId: selectedWorker.nodeId || 'N/A',
            targetAgentId: agentId
          });
          Logger.info(`Master node (${this.nodeId}) initiated delegation of agent ${agentId} to worker ${selectedWorker.id}.`);
          
          // CONCEPTUAL RPC CALL: Master commands worker to create agent
          // In a real system: await this.rpcCallToWorker(selectedWorker.id, 'createAgent', { agentId, streamConfig, masterNodeId: this.nodeId });
          // Worker's handleMasterCommand_CreateAgent would be invoked.
          Logger.info(`Master node (${this.nodeId}) (simulated) RPC: Commanding worker ${selectedWorker.id} to execute handleMasterCommand_CreateAgent for agent ${agentId}.`);

          // Optimistically update worker's load. Worker would confirm via heartbeat/report.
          const workerData = this.workerNodes.get(selectedWorker.id);
          if(workerData) {
            workerData.currentLoad = (workerData.currentLoad || 0) + 1;
            this.workerNodes.set(selectedWorker.id, workerData);
            Logger.debug(`Master optimistically updated load for worker ${selectedWorker.id} to ${workerData.currentLoad}/${workerData.capacity}`);
          }
          
          return agentId; // Master's role for this agentId creation is done (delegation initiated).
        } catch (dbError) {
          Logger.error(`Master node (${this.nodeId}) failed to create/log delegation placeholder for agent ${agentId} to worker ${selectedWorker.id}`, { error: dbError.message });
          this.agentToWorkerMap.delete(agentId); // Rollback map
          throw ErrorHandler.createCustomError(
            'FleetControllerError',
            `Master failed to record/log delegation for ${agentId}: ${dbError.message}`,
            { originalError: dbError.message }
          );
        }
      } else {
        Logger.warn(`Master node (${this.nodeId}): No available/suitable workers. Attempting to create agent ${agentId} locally on master.`);
        // Fall through to local creation by master if it has capacity.
      }
    }

    // Local creation logic (for standalone, worker, or master fallback/local agents)
    return this._createAgentLocally(agentId);
  }

  async _createAgentLocally(agentId, streamConfig = null) { // streamConfig might be used later by agent
    // This node (worker, or master doing local creation) is creating the agent.
    Logger.info(`Node ${this.nodeId} (Role: ${this.nodeRole}) creating agent ${agentId} locally.`);
    try {
      // The primary DB record for the agent is created by the node hosting it.
      await DatabaseManager.createAgent(agentId, 'idle', this.nodeId);
      this.agents.set(agentId, {
        id: agentId, // Storing id for easier access
        status: 'idle',
        streamId: null, // streamConfig could set this if provided
        lastUpdated: new Date(),
        nodeId: this.nodeId
      });
      await DatabaseManager.logAgentEvent(agentId, 'created_locally', { nodeId: this.nodeId });
      Logger.info(`Agent ${agentId} created successfully on node ${this.nodeId}.`);
      return agentId;
    } catch (error) {
      Logger.error(`Failed to create agent ${agentId} locally on node ${this.nodeId}`, {
        error: error.message,
        stack: error.stack,
        agentId,
        nodeId: this.nodeId
      });
      throw ErrorHandler.createCustomError(
        'FleetControllerError',
        `Failed to create agent ${agentId} locally on ${this.nodeId}: ${error.message}`,
        { originalError: error.message, stack: error.stack }
      );
    }
  }

  async destroyAgent(agentId, reason = 'manual_shutdown') {
    if (this.distributedEnabled && this.nodeRole === 'master') {
      const workerId = this.agentToWorkerMap.get(agentId);
      if (workerId) {
        const workerData = this.workerNodes.get(workerId);
        if (workerData) {
          Logger.info(`Master node (${this.nodeId}) commanding worker ${workerId} (Node: ${workerData.nodeId || 'N/A'}) to destroy agent ${agentId}. Reason: ${reason}`);
          try {
            // Master updates its placeholder/delegation record status.
            await DatabaseManager.updateAgentStatus(agentId, 'terminating_delegated', null, this.nodeId); // Master updates its view of agent state
            await DatabaseManager.logAgentEvent(agentId, 'delegated_destruction_initiated', { 
              masterNodeId: this.nodeId, 
              workerId: workerId,
              workerNodeId: workerData.nodeId || 'N/A',
              targetAgentId: agentId,
              reason
            });

            // CONCEPTUAL RPC CALL: Master commands worker to destroy agent
            // In a real system: await this.rpcCallToWorker(workerId, 'destroyAgent', { agentId, reason, masterNodeId: this.nodeId });
            // Worker's handleMasterCommand_DestroyAgent would be invoked.
            Logger.info(`Master node (${this.nodeId}) (simulated) RPC: Commanding worker ${workerId} to execute handleMasterCommand_DestroyAgent for agent ${agentId}.`);

            this.agentToWorkerMap.delete(agentId);
            if (workerData.currentLoad > 0) workerData.currentLoad--;
            this.workerNodes.set(workerId, workerData); // Update worker load
            Logger.debug(`Master optimistically updated load for worker ${workerId} to ${workerData.currentLoad}/${workerData.capacity} after destroy command.`);

            Logger.info(`Master node (${this.nodeId}) completed delegation steps for destroying agent ${agentId} on worker ${workerId}.`);
            return; // Master's job for this delegated agent is done (delegation command sent).
          } catch (dbError) {
            Logger.error(`Master node (${this.nodeId}) failed to update/log delegation status for destroying agent ${agentId}`, { error: dbError.message });
            throw ErrorHandler.createCustomError(
                'FleetControllerError',
                `Master failed to record/log delegated destruction for ${agentId}: ${dbError.message}`,
                { originalError: dbError.message }
            );
          }
        } else {
          Logger.warn(`Master node (${this.nodeId}): Worker ${workerId} (for agent ${agentId}) not found in workerNodes map. Removing from agentToWorkerMap. Agent might be orphaned or already destroyed.`);
          this.agentToWorkerMap.delete(agentId); // Clean up the map anyway.
          // Do not fall through to local destruction by master unless master is sure it owns it.
          // If the agent truly existed on a worker that disappeared, it's an orphan scenario for the worker.
          // Master has done its part by removing the mapping.
          return; // Avoid master trying to locally destroy an agent it doesn't own.
        }
      }
      // If agentId was not in agentToWorkerMap, it implies it's either a local agent on master or unknown.
      // Fall through to local destruction by master.
      Logger.info(`Agent ${agentId} not found in agentToWorkerMap. Assuming it is a local agent on master or unknown. Proceeding with local destruction attempt.`);
    }

    // Local destruction logic (for standalone, worker, or master's own agents)
    return this._destroyAgentLocally(agentId, reason);
  }

  async _destroyAgentLocally(agentId, reason = 'manual_shutdown') {
    // This node (worker, or master doing local destruction) is destroying the agent.
    const agentInfo = this.agents.get(agentId);
    if (!agentInfo) {
      Logger.warn(`Node ${this.nodeId} (Role: ${this.nodeRole}) attempted to destroy non-existent local agent ${agentId}.`);
      return;
    }

    Logger.info(`Node ${this.nodeId} (Role: ${this.nodeRole}) destroying agent ${agentId} locally. Reason: ${reason}`);
    try {
      // TODO: Implement actual agent destruction (e.g., closing browser instance via AgentManager.destroy(agentId))
      await DatabaseManager.logAgentEvent(agentId, 'destroyed_locally', { reason, nodeId: this.nodeId });
      await DatabaseManager.updateAgentStatus(agentId, 'terminated', null, this.nodeId); // ensure status is updated for this node's record
      this.agents.delete(agentId);
      this.errorCounts.delete(agentId);
      Logger.info(`Agent ${agentId} destroyed successfully locally on node ${this.nodeId}.`);
    } catch (error) {
      Logger.error(`Failed to destroy agent ${agentId} locally on node ${this.nodeId}`, { error: error.message });
      throw ErrorHandler.createCustomError(
        'FleetControllerError',
        `Failed to destroy agent ${agentId} locally on ${this.nodeId}: ${error.message}`,
        { originalError: error.message }
      );
    }
  }

  async updateAgentStatus(agentId, status, streamId = null) {
    const agent = this.agents.get(agentId);
    if (!agent) {
      // If this is a master node, the agent might be on a worker.
      if (this.distributedEnabled && this.nodeRole === 'master' && this.agentToWorkerMap.has(agentId)) {
        const workerId = this.agentToWorkerMap.get(agentId);
        Logger.info(`Master node (${this.nodeId}) received updateAgentStatus for agent ${agentId} (on worker ${workerId}). Master will update its placeholder for this agent if necessary. Original update should come from worker heartbeat/event.`);
        // Master updates its own DB record if it maintains one for delegated agents.
        try {
            await DatabaseManager.updateAgentStatus(agentId, status, streamId, this.nodeId); // Master updates its view
            Logger.info(`Master node (${this.nodeId}) updated its tracked status for delegated agent ${agentId} to ${status}.`);
        } catch(dbError) {
            Logger.error(`Master node (${this.nodeId}) failed to update its tracked status for delegated agent ${agentId}`, { error: dbError.message });
        }
        return; // Master does not manage this.agents entry for delegated agents.
      }
      throw ErrorHandler.createCustomError(
        'FleetControllerError',
        'Agent not found',
        { agentId }
      );
    }

    try {
      await DatabaseManager.updateAgentStatus(agentId, status, streamId);
      this.agents.set(agentId, {
        status,
        streamId,
        lastUpdated: new Date()
      });
      
      Logger.info('Agent status updated', { agentId, status, streamId });
    } catch (error) {
      throw ErrorHandler.createCustomError(
        'FleetControllerError',
        'Failed to update agent status',
        { agentId, status, streamId, originalError: error.message }
      );
    }
  }

  cleanup() {
    if (this.healthCheckTimer) {
      clearInterval(this.healthCheckTimer);
    }
    Logger.info('Fleet Controller cleanup completed');
  }

  // Placeholder for master to select a worker (simple round-robin or load-based)
  selectWorkerForNewAgent() {
    // Simple strategy: pick worker with fewest agents (lowest currentLoad)
    // More sophisticated: consider capacity, resource usage, etc.
    let bestWorker = null;
    let minLoadScore = Infinity; // Lower is better, based on load ratio and available capacity

    Logger.debug(`Selecting worker for new agent. Master (${this.nodeId}) evaluating ${this.workerNodes.size} registered workers.`);
    // TODO: (Master Node Resource Optimization) The selection logic should also heavily consider
    // reported worker resource health (CPU, memory, etc.) from heartbeats.
    // A worker might have low agent load but high resource consumption due to other factors.
    // This could be factored into the 'currentScore'.
    // Logger.info(`Master node (${this.nodeId}) selectWorker: Also considering worker resource health (CPU/memory) in selection.`);

    for (const [workerId, workerData] of this.workerNodes) {
      Logger.debug(`Master (${this.nodeId}) evaluating worker ${workerId} (Node: ${workerData.nodeId || 'N/A'})`, { 
        status: workerData.status, 
        capacity: workerData.capacity, 
        currentLoad: workerData.currentLoad 
        // TODO: Add workerData.cpuUsage, workerData.memoryUsage to this log once available from heartbeats
      });
      
      const capacity = workerData.capacity || 0;
      const currentLoad = workerData.currentLoad || 0;

      // Worker must be in a ready state (e.g., 'registered', 'active', or status not yet set but has capacity)
      // and have defined capacity.
      if ((!workerData.status || workerData.status === 'registered' || workerData.status === 'active') && capacity > 0) {
        if (currentLoad < capacity) {
          // Calculate a score. Lower is better.
          // Prioritize workers with a lower load ratio (currentLoad / capacity).
          // As a tie-breaker, or secondary factor, prefer workers with more absolute available spots.
          const loadRatio = currentLoad / capacity;
          const availableSlots = capacity - currentLoad;
          
          // Example scoring: Primary factor is loadRatio, secondary is -(availableSlots) to favor more slots.
          // Multiplying availableSlots by a small negative number makes more slots better (smaller score).
          const currentScore = loadRatio - (availableSlots * 0.001); 

          if (currentScore < minLoadScore) {
            minLoadScore = currentScore;
            bestWorker = {id: workerId, ...workerData}; // id is the map key (registration ID), workerData.nodeId is the worker's own reported ID
            Logger.debug(`Master (${this.nodeId}) found new best worker candidate: ${workerId} (Node: ${bestWorker.nodeId}), Score: ${currentScore.toFixed(3)} (Load: ${currentLoad}/${capacity})`);
          }
        } else {
          Logger.debug(`Master (${this.nodeId}): Worker ${workerId} (Node: ${workerData.nodeId || 'N/A'}) is at full capacity (${currentLoad}/${capacity}).`);
        }
      } else {
        Logger.debug(`Master (${this.nodeId}): Worker ${workerId} (Node: ${workerData.nodeId || 'N/A'}) is not suitable (Status: ${workerData.status}, Capacity: ${capacity}).`);
      }
    }

    if (bestWorker) {
      Logger.info(`Master (${this.nodeId}) selected worker ${bestWorker.id} (Node: ${bestWorker.nodeId || 'N/A'}) for new agent. Load: ${bestWorker.currentLoad}/${bestWorker.capacity}. Score: ${minLoadScore.toFixed(3)}`);
    } else {
      Logger.warn(`Master (${this.nodeId}) found no suitable worker for new agent assignment among ${this.workerNodes.size} workers.`);
      // TODO: (Master Node Resource Optimization) If no *healthy* and available worker found, log this specifically.
    }
    return bestWorker;
  }

  // --- Centralized Control Commands (Master to Worker) ---
  async sendGlobalCommandToWorkers(commandType, payload) {
    if (this.nodeRole !== 'master') {
      Logger.warn(`Node ${this.nodeId} (Role: ${this.nodeRole}) attempted to send a global command but is not master. Ignoring.`);
      return { success: false, message: 'Only master can send global commands.', sentTo: 0 };
    }
    if (this.workerNodes.size === 0) {
      Logger.info(`Master node (${this.nodeId}) has no registered workers to send global command '${commandType}'.`);
      return { success: true, message: 'No workers to send command to.', sentTo: 0 };
    }

    Logger.info(`Master node (${this.nodeId}) is broadcasting global command '${commandType}' to ${this.workerNodes.size} worker(s). Payload:`, payload);
    let successfulSends = 0;
    for (const [workerId, workerData] of this.workerNodes) {
      // CONCEPTUAL RPC CALL to each worker
      // In a real system: await this.rpcCallToWorker(workerId, 'handleGlobalCommand', { commandType, payload });
      Logger.info(`Master node (${this.nodeId}) (simulated) RPC: Sending global command '${commandType}' to worker ${workerId} (Node: ${workerData.nodeId || 'N/A'}).`);
      // Simulate success for now
      successfulSends++;
    }
    Logger.info(`Master node (${this.nodeId}) finished broadcasting global command '${commandType}'. Sent to ${successfulSends} worker(s).`);
    return { success: true, message: `Command '${commandType}' broadcasted.`, sentTo: successfulSends };
  }

  // --- Worker-Side Command Handlers (called by Master via conceptual RPC) ---
  async handleMasterCommand_CreateAgent(agentId, streamConfig = null, masterNodeIdInitiator) {
    if (this.nodeRole !== 'worker') {
      Logger.error(`Node ${this.nodeId} (Role: ${this.nodeRole}) received CreateAgent command from ${masterNodeIdInitiator} but is not a worker. Ignoring.`);
      return { success: false, error: 'Not a worker node' };
    }
    Logger.info(`Worker node (${this.nodeId}) received command from Master (${masterNodeIdInitiator}) to create agent ${agentId}.`);
    try {
      await this._createAgentLocally(agentId, streamConfig);
      // CONCEPTUAL CALLBACK/REPORT TO MASTER:
      // In a real system: await this.rpcCallToMaster('reportAgentCreated', { workerNodeId: this.nodeId, agentId, status: 'idle' });
      // Master's handleWorkerReport_AgentCreated would be invoked.
      Logger.info(`Worker node (${this.nodeId}) successfully created agent ${agentId}. It would now report success to master (${masterNodeIdInitiator}).`);
      return { success: true, agentId };
    } catch (error) {
      Logger.error(`Worker node (${this.nodeId}) failed to create agent ${agentId} as commanded by master ${masterNodeIdInitiator}.`, { error: error.message });
      // CONCEPTUAL CALLBACK/REPORT TO MASTER:
      // In a real system: await this.rpcCallToMaster('reportAgentCreationFailed', { workerNodeId: this.nodeId, agentId, error: error.message });
      Logger.info(`Worker node (${this.nodeId}) failed to create agent ${agentId}. It would now report failure to master (${masterNodeIdInitiator}).`);
      return { success: false, error: error.message, agentId };
    }
  }

  async handleMasterCommand_DestroyAgent(agentId, reason = 'master_command', masterNodeIdInitiator) {
    if (this.nodeRole !== 'worker') {
      Logger.error(`Node ${this.nodeId} (Role: ${this.nodeRole}) received DestroyAgent command from ${masterNodeIdInitiator} but is not a worker. Ignoring.`);
      return { success: false, error: 'Not a worker node' };
    }
    Logger.info(`Worker node (${this.nodeId}) received command from Master (${masterNodeIdInitiator}) to destroy agent ${agentId}. Reason: ${reason}`);
    try {
      await this._destroyAgentLocally(agentId, reason);
      // CONCEPTUAL CALLBACK/REPORT TO MASTER:
      // In a real system: await this.rpcCallToMaster('reportAgentDestroyed', { workerNodeId: this.nodeId, agentId });
      // Master's handleWorkerReport_AgentDestroyed would be invoked.
      Logger.info(`Worker node (${this.nodeId}) successfully destroyed agent ${agentId}. It would now report success to master (${masterNodeIdInitiator}).`);
      return { success: true, agentId };
    } catch (error) {
      Logger.error(`Worker node (${this.nodeId}) failed to destroy agent ${agentId} as commanded by master ${masterNodeIdInitiator}.`, { error: error.message });
      // CONCEPTUAL CALLBACK/REPORT TO MASTER:
      // In a real system: await this.rpcCallToMaster('reportAgentDestructionFailed', { workerNodeId: this.nodeId, agentId, error: error.message });
      Logger.info(`Worker node (${this.nodeId}) failed to destroy agent ${agentId}. It would now report failure to master (${masterNodeIdInitiator}).`);
      return { success: false, error: error.message, agentId };
    }
  }

  async handleMasterCommand_GlobalBroadcast(commandType, payload, masterNodeIdInitiator) {
    if (this.nodeRole !== 'worker') {
      Logger.error(`Node ${this.nodeId} (Role: ${this.nodeRole}) received GlobalBroadcast command from ${masterNodeIdInitiator} but is not a worker. Ignoring.`);
      return { success: false, error: 'Not a worker node' };
    }
    Logger.info(`Worker node (${this.nodeId}) received global command '${commandType}' from Master (${masterNodeIdInitiator}). Payload:`, payload);
    // TODO: Implement actual logic for different commandTypes
    // e.g., if (commandType === 'PAUSE_ALL_ACTIVITY') { this.pauseAllLocalAgents(); }
    //       else if (commandType === 'REFRESH_CONFIG') { this.config.reloadConfig(payload); }
    Logger.info(`Worker node (${this.nodeId}) (simulated) processed global command '${commandType}'. It would now report acknowledge/status to master if required.`);
    return { success: true, message: `Command '${commandType}' acknowledged.` };
  }

  // --- Master-Side Worker Report Handlers ---
  async handleWorkerReport_AgentCreated(workerNodeId, agentId, details = {}) {
    if (this.nodeRole !== 'master') {
      Logger.error(`Node ${this.nodeId} (Role: ${this.nodeRole}) received AgentCreated report but is not master. Ignoring.`);
      return;
    }
    const workerRegId = Object.keys(this.workerNodes).find(regId => this.workerNodes[regId]?.nodeId === workerNodeId);
    if (!this.agentToWorkerMap.has(agentId) || this.agentToWorkerMap.get(agentId) !== workerRegId) {
        Logger.warn(`Master (${this.nodeId}) received AgentCreated report for agent ${agentId} from worker ${workerNodeId}, but this agent is not mapped or mapped to a different worker. Ignoring or re-evaluating mapping.`);
        // Could be a late report or a mapping issue.
        return;
    }

    Logger.info(`Master (${this.nodeId}) received report: Agent ${agentId} created successfully on worker ${workerNodeId}. Updating master's view of shared agent state. Details:`, details);
    try {
        // Update master's placeholder record for this agent.
        // Assuming status from worker would be 'idle' or 'active_on_worker'
        const newStatus = details.status || 'active_on_worker';
        await DatabaseManager.updateAgentStatus(agentId, newStatus, details.streamId || null, this.nodeId);
        await DatabaseManager.logAgentEvent(agentId, 'delegation_confirmed_created', {
            masterNodeId: this.nodeId,
            workerNodeId,
            reportedStatus: newStatus
        });
        Logger.info(`Master (${this.nodeId}) updated status for delegated agent ${agentId} to ${newStatus} based on worker report.`);
    } catch (error) {
        Logger.error(`Master (${this.nodeId}) failed to update DB for agent ${agentId} after creation report from worker ${workerNodeId}.`, { error: error.message });
    }
  }

  async handleWorkerReport_AgentDestroyed(workerNodeId, agentId, details = {}) {
    if (this.nodeRole !== 'master') {
      Logger.error(`Node ${this.nodeId} (Role: ${this.nodeRole}) received AgentDestroyed report but is not master. Ignoring.`);
      return;
    }
    const workerRegId = Object.keys(this.workerNodes).find(regId => this.workerNodes[regId]?.nodeId === workerNodeId);
    // Check if agent was mapped to this worker. It should have been removed from map when master initiated destruction command.
    // However, worker might report destruction for other reasons (e.g. local error on worker).
    Logger.info(`Master (${this.nodeId}) received report: Agent ${agentId} destroyed on worker ${workerNodeId}. Updating master's view of shared agent state. Details:`, details);
    try {
        // Update master's placeholder record for this agent.
        await DatabaseManager.updateAgentStatus(agentId, 'terminated_on_worker', null, this.nodeId);
        await DatabaseManager.logAgentEvent(agentId, 'delegation_confirmed_destroyed', {
            masterNodeId: this.nodeId,
            workerNodeId,
            reason: details.reason || 'worker_reported_destruction'
        });
        Logger.info(`Master (${this.nodeId}) marked delegated agent ${agentId} as terminated_on_worker based on report from ${workerNodeId}.`);
        // Ensure it's removed from agentToWorkerMap if it wasn't already (e.g. worker-initiated destruction)
        if (this.agentToWorkerMap.has(agentId) && this.agentToWorkerMap.get(agentId) === workerRegId) {
            this.agentToWorkerMap.delete(agentId);
            Logger.info(`Master (${this.nodeId}) removed agent ${agentId} from agentToWorkerMap after destruction report from worker ${workerNodeId}.`);
            // Adjust worker load if master hadn't already accounted for it
            const workerData = this.workerNodes.get(workerRegId);
            if (workerData && workerData.currentLoad > 0) {
                workerData.currentLoad--;
                this.workerNodes.set(workerRegId, workerData);
                Logger.debug(`Master (${this.nodeId}) adjusted load for worker ${workerRegId} to ${workerData.currentLoad}/${workerData.capacity} after destruction report.`);
            }
        }
    } catch (error) {
        Logger.error(`Master (${this.nodeId}) failed to update DB for agent ${agentId} after destruction report from worker ${workerNodeId}.`, { error: error.message });
    }
  }
}

module.exports = new FleetController(); 