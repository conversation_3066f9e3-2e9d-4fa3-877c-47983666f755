const express = require('express');
const http = require('http');
const WebSocket = require('ws');
const path = require('path');
const Logger = require('../utils/Logger');
const AgentManager = require('../agents/AgentManager');
const StreamManager = require('../streams/StreamManager');
const ResourceMonitor = require('../utils/ResourceMonitor');

class DashboardServer {
  constructor() {
    // Configuration
    this.port = parseInt(process.env.DASHBOARD_PORT || '3000');
    this.host = process.env.DASHBOARD_HOST || 'localhost';
    this.wsHeartbeatInterval = parseInt(process.env.WS_HEARTBEAT_INTERVAL || '30000'); // 30 seconds
    
    // Initialize Express app
    this.app = express();
    this.server = http.createServer(this.app);
    this.wss = new WebSocket.Server({ server: this.server });
    
    // Client tracking
    this.clients = new Set();
    this.clientHeartbeats = new Map();
    
    // Update intervals
    this.updateIntervals = new Map();
  }

  async initialize() {
    try {
      Logger.info('Initializing Dashboard Server');
      
      // Configure Express middleware
      this.setupMiddleware();
      
      // Configure routes
      this.setupRoutes();
      
      // Configure WebSocket
      this.setupWebSocket();
      
      // Start server
      await this.startServer();
      
      // Start update intervals
      this.startUpdateIntervals();
      
      Logger.info('Dashboard Server initialized successfully');
    } catch (error) {
      Logger.error('Failed to initialize Dashboard Server', { error: error.message });
      throw error;
    }
  }

  setupMiddleware() {
    // Parse JSON bodies
    this.app.use(express.json());

    // Check if UI build directory exists before serving static files
    const uiBuildPath = path.join(__dirname, 'ui/build');
    if (require('fs').existsSync(uiBuildPath)) {
      this.app.use(express.static(uiBuildPath));
    } else {
      Logger.warn('UI build directory not found, skipping static file serving');
    }

    // CORS middleware
    this.app.use((req, res, next) => {
      res.header('Access-Control-Allow-Origin', '*');
      res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept');
      next();
    });

    // Error handling middleware
    this.app.use((err, req, res, next) => {
      Logger.error('Express error', { error: err.message });
      res.status(500).json({ error: 'Internal server error' });
    });
  }

  setupRoutes() {
    // Simple API routes without parameters for now
    this.app.get('/api/status', (req, res) => {
      res.json({
        status: 'running',
        message: 'Twitch Fleet Manager API',
        version: '1.0.0',
        timestamp: new Date().toISOString()
      });
    });

    this.app.get('/api/agents', this.getAgents.bind(this));
    this.app.get('/api/streams', this.getStreams.bind(this));
    this.app.get('/api/metrics', this.getMetrics.bind(this));

    // Default route
    this.app.get('/', (req, res) => {
      res.json({
        message: 'Twitch Fleet Manager API',
        version: '1.0.0',
        endpoints: [
          'GET /api/status',
          'GET /api/agents',
          'GET /api/streams',
          'GET /api/metrics'
        ]
      });
    });
  }

  setupWebSocket() {
    this.wss.on('connection', (ws) => {
      // Add client to set
      this.clients.add(ws);
      this.setupClientHeartbeat(ws);
      
      // Send initial state
      this.sendInitialState(ws);
      
      ws.on('message', this.handleWebSocketMessage.bind(this, ws));
      
      ws.on('close', () => {
        this.clients.delete(ws);
        this.clientHeartbeats.delete(ws);
      });
      
      ws.on('error', (error) => {
        Logger.error('WebSocket error', { error: error.message });
        ws.terminate();
      });
    });
  }

  setupClientHeartbeat(ws) {
    // Set up ping interval
    const heartbeat = setInterval(() => {
      if (ws.readyState === WebSocket.OPEN) {
        ws.ping();
      }
    }, this.wsHeartbeatInterval);
    
    this.clientHeartbeats.set(ws, heartbeat);
    
    ws.on('close', () => {
      clearInterval(heartbeat);
    });
  }

  async startServer() {
    return new Promise((resolve, reject) => {
      this.server.listen(this.port, this.host, (err) => {
        if (err) {
          reject(err);
          return;
        }
        Logger.info(`Dashboard server listening on ${this.host}:${this.port}`);
        resolve();
      });
    });
  }

  startUpdateIntervals() {
    // Agent updates every 5 seconds
    this.updateIntervals.set('agents', setInterval(() => {
      this.broadcastAgentUpdates();
    }, 5000));
    
    // Stream updates every 10 seconds
    this.updateIntervals.set('streams', setInterval(() => {
      this.broadcastStreamUpdates();
    }, 10000));
    
    // Metrics updates every second
    this.updateIntervals.set('metrics', setInterval(() => {
      this.broadcastMetricsUpdates();
    }, 1000));
  }

  // WebSocket message handling
  handleWebSocketMessage(ws, message) {
    try {
      const data = JSON.parse(message);
      
      switch (data.type) {
        case 'subscribe':
          this.handleSubscribe(ws, data);
          break;
        case 'unsubscribe':
          this.handleUnsubscribe(ws, data);
          break;
        case 'command':
          this.handleCommand(ws, data);
          break;
        default:
          Logger.warn('Unknown WebSocket message type', { type: data.type });
      }
    } catch (error) {
      Logger.error('Failed to handle WebSocket message', { error: error.message });
    }
  }

  handleSubscribe(ws, data) {
    // Add subscription tracking
    ws.subscriptions = ws.subscriptions || new Set();
    ws.subscriptions.add(data.channel);
    
    // Send initial data for subscription
    this.sendSubscriptionData(ws, data.channel);
  }

  handleUnsubscribe(ws, data) {
    if (ws.subscriptions) {
      ws.subscriptions.delete(data.channel);
    }
  }

  async handleCommand(ws, data) {
    try {
      switch (data.command) {
        case 'startAgent':
          await AgentManager.startAgent(data.agentId);
          break;
        case 'stopAgent':
          await AgentManager.stopAgent(data.agentId);
          break;
        case 'restartAgent':
          await AgentManager.restartAgent(data.agentId);
          break;
        default:
          Logger.warn('Unknown command', { command: data.command });
      }
    } catch (error) {
      Logger.error('Failed to execute command', {
        command: data.command,
        error: error.message
      });
      ws.send(JSON.stringify({
        type: 'error',
        command: data.command,
        error: error.message
      }));
    }
  }

  // API route handlers
  async getAgents(req, res) {
    try {
      // For now, return mock data until AgentManager is working
      res.json({
        agents: [],
        count: 0,
        status: 'No agents running yet'
      });
    } catch (error) {
      Logger.error('Failed to get agents', { error: error.message });
      res.status(500).json({ error: 'Failed to get agents' });
    }
  }

  async getAgent(req, res) {
    try {
      const agent = await AgentManager.getAgent(req.params.id);
      if (!agent) {
        res.status(404).json({ error: 'Agent not found' });
        return;
      }
      res.json(agent);
    } catch (error) {
      Logger.error('Failed to get agent', {
        agentId: req.params.id,
        error: error.message
      });
      res.status(500).json({ error: 'Failed to get agent' });
    }
  }

  async controlAgent(req, res) {
    try {
      const { action } = req.body;
      const agentId = req.params.id;
      
      switch (action) {
        case 'start':
          await AgentManager.startAgent(agentId);
          break;
        case 'stop':
          await AgentManager.stopAgent(agentId);
          break;
        case 'restart':
          await AgentManager.restartAgent(agentId);
          break;
        default:
          res.status(400).json({ error: 'Invalid action' });
          return;
      }
      
      res.json({ success: true });
    } catch (error) {
      Logger.error('Failed to control agent', {
        agentId: req.params.id,
        error: error.message
      });
      res.status(500).json({ error: 'Failed to control agent' });
    }
  }

  async getStreams(req, res) {
    try {
      // For now, return mock data until StreamManager is working
      res.json({
        streams: [],
        count: 0,
        status: 'No streams being monitored yet'
      });
    } catch (error) {
      Logger.error('Failed to get streams', { error: error.message });
      res.status(500).json({ error: 'Failed to get streams' });
    }
  }

  async getStream(req, res) {
    try {
      const stream = await StreamManager.getStream(req.params.id);
      if (!stream) {
        res.status(404).json({ error: 'Stream not found' });
        return;
      }
      res.json(stream);
    } catch (error) {
      Logger.error('Failed to get stream', {
        streamId: req.params.id,
        error: error.message
      });
      res.status(500).json({ error: 'Failed to get stream' });
    }
  }

  async getMetrics(req, res) {
    try {
      // For now, return mock data until ResourceMonitor is working
      res.json({
        cpu: 0,
        memory: 0,
        agents: 0,
        streams: 0,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      Logger.error('Failed to get metrics', { error: error.message });
      res.status(500).json({ error: 'Failed to get metrics' });
    }
  }

  async getMetricsHistory(req, res) {
    try {
      const { duration } = req.query;
      const history = await ResourceMonitor.getMetricsHistory(duration);
      res.json(history);
    } catch (error) {
      Logger.error('Failed to get metrics history', { error: error.message });
      res.status(500).json({ error: 'Failed to get metrics history' });
    }
  }

  async getLogs(req, res) {
    try {
      const { limit, level } = req.query;
      const logs = await Logger.getLogs(limit, level);
      res.json(logs);
    } catch (error) {
      Logger.error('Failed to get logs', { error: error.message });
      res.status(500).json({ error: 'Failed to get logs' });
    }
  }

  async searchLogs(req, res) {
    try {
      const { query, from, to, level } = req.query;
      const logs = await Logger.searchLogs(query, from, to, level);
      res.json(logs);
    } catch (error) {
      Logger.error('Failed to search logs', { error: error.message });
      res.status(500).json({ error: 'Failed to search logs' });
    }
  }

  // WebSocket broadcast methods
  broadcastAgentUpdates() {
    this.broadcast('agents', async () => {
      // Return mock data for now since AgentManager isn't fully implemented
      return {
        type: 'agentUpdate',
        agents: []
      };
    });
  }

  broadcastStreamUpdates() {
    this.broadcast('streams', async () => {
      // Return mock data for now since StreamManager isn't fully implemented
      return {
        type: 'streamUpdate',
        streams: []
      };
    });
  }

  broadcastMetricsUpdates() {
    this.broadcast('metrics', async () => {
      // Return mock data for now since ResourceMonitor isn't fully implemented
      return {
        type: 'metricsUpdate',
        metrics: {
          cpu: 0,
          memory: 0,
          agents: 0,
          streams: 0,
          timestamp: new Date().toISOString()
        }
      };
    });
  }

  async broadcast(channel, dataProvider) {
    try {
      const data = await dataProvider();
      
      for (const client of this.clients) {
        if (client.readyState === WebSocket.OPEN &&
            (!client.subscriptions || client.subscriptions.has(channel))) {
          client.send(JSON.stringify(data));
        }
      }
    } catch (error) {
      Logger.error('Failed to broadcast updates', {
        channel,
        error: error.message
      });
    }
  }

  async sendInitialState(ws) {
    try {
      // Send initial agents state (mock data for now)
      ws.send(JSON.stringify({
        type: 'initialAgents',
        agents: []
      }));

      // Send initial streams state (mock data for now)
      ws.send(JSON.stringify({
        type: 'initialStreams',
        streams: []
      }));

      // Send initial metrics (mock data for now)
      ws.send(JSON.stringify({
        type: 'initialMetrics',
        metrics: {
          cpu: 0,
          memory: 0,
          agents: 0,
          streams: 0,
          timestamp: new Date().toISOString()
        }
      }));
    } catch (error) {
      Logger.error('Failed to send initial state', { error: error.message });
    }
  }

  async sendSubscriptionData(ws, channel) {
    try {
      let data;

      switch (channel) {
        case 'agents':
          data = {
            type: 'initialAgents',
            agents: [] // Mock data for now
          };
          break;
        case 'streams':
          data = {
            type: 'initialStreams',
            streams: [] // Mock data for now
          };
          break;
        case 'metrics':
          data = {
            type: 'initialMetrics',
            metrics: {
              cpu: 0,
              memory: 0,
              agents: 0,
              streams: 0,
              timestamp: new Date().toISOString()
            }
          };
          break;
        default:
          return;
      }

      ws.send(JSON.stringify(data));
    } catch (error) {
      Logger.error('Failed to send subscription data', {
        channel,
        error: error.message
      });
    }
  }

  cleanup() {
    try {
      // Clear all update intervals
      for (const [key, interval] of this.updateIntervals) {
        clearInterval(interval);
      }
      this.updateIntervals.clear();
      
      // Clear all client heartbeats
      for (const [ws, heartbeat] of this.clientHeartbeats) {
        clearInterval(heartbeat);
      }
      this.clientHeartbeats.clear();
      
      // Close all WebSocket connections
      for (const client of this.clients) {
        client.terminate();
      }
      this.clients.clear();
      
      // Close HTTP server
      if (this.server) {
        this.server.close();
      }

      Logger.info('Dashboard Server cleanup completed');
    } catch (error) {
      Logger.error('Failed to cleanup Dashboard Server', { error: error.message });
    }
  }
}

module.exports = new DashboardServer(); 