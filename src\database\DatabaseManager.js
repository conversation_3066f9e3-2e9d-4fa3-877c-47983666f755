const knex = require('knex');
const path = require('path');
const fs = require('fs');
const Logger = require('../utils/Logger');
const ErrorHandler = require('../utils/ErrorHandler');

class DatabaseManager {
  constructor() {
    this.dbPath = process.env.DB_PATH || path.join(process.cwd(), 'data', 'fleet.db');
    this.ensureDatabaseDirectory();
    this.initializeDatabase();
  }

  ensureDatabaseDirectory() {
    const dbDir = path.dirname(this.dbPath);
    if (!fs.existsSync(dbDir)) {
      fs.mkdirSync(dbDir, { recursive: true });
    }
  }

  initializeDatabase() {
    this.db = knex({
      client: 'sqlite3',
      connection: {
        filename: this.dbPath
      },
      useNullAsDefault: true,
      pool: {
        min: 2,
        max: 10,
        afterCreate: (conn, done) => {
          // Enable foreign key support
          conn.run('PRAGMA foreign_keys = ON', done);
        }
      }
    });

    // Log database connection
    Logger.info('Database initialized', { path: this.dbPath });
  }

  async createTables() {
    try {
      // Create agents table if it doesn't exist
      const agentsExists = await this.db.schema.hasTable('agents');
      if (!agentsExists) {
        await this.db.schema.createTable('agents', table => {
          table.string('id').primary();
          table.string('status').notNullable();
          table.string('stream_id').nullable();
          table.string('node_id').nullable();
          table.integer('watch_time').defaultTo(0);
          table.timestamps(true, true);
          table.index('status');
          table.index('node_id');
        });
      } else {
        // Check if node_id column exists, if not add it
        const hasNodeId = await this.db.schema.hasColumn('agents', 'node_id');
        if (!hasNodeId) {
          await this.db.schema.alterTable('agents', table => {
            table.string('node_id').nullable();
            table.index('node_id');
          });
          Logger.info('Added node_id column to agents table');
        }
      }

      // Create streams table if it doesn't exist
      const streamsExists = await this.db.schema.hasTable('streams');
      if (!streamsExists) {
        await this.db.schema.createTable('streams', table => {
          table.string('id').primary();
          table.string('channel_name').notNullable();
          table.string('game_id').notNullable();
          table.integer('viewer_count').defaultTo(0);
          table.timestamp('started_at').notNullable();
          table.timestamp('created_at').defaultTo(this.db.fn.now());
          table.timestamp('updated_at').defaultTo(this.db.fn.now());
        });
      }

      // Create agent_events table if it doesn't exist
      const agentEventsExists = await this.db.schema.hasTable('agent_events');
      if (!agentEventsExists) {
        await this.db.schema.createTable('agent_events', table => {
          table.increments('id').primary();
          table.string('agent_id').notNullable();
          table.string('event_type').notNullable();
          table.json('event_data').nullable();
          table.timestamp('created_at').defaultTo(this.db.fn.now());
          table.foreign('agent_id').references('agents.id');
        });
      }

      // Create stream_events table if it doesn't exist
      const streamEventsExists = await this.db.schema.hasTable('stream_events');
      if (!streamEventsExists) {
        await this.db.schema.createTable('stream_events', table => {
          table.increments('id').primary();
          table.string('stream_id').notNullable();
          table.string('event_type').notNullable();
          table.json('event_data').nullable();
          table.timestamp('created_at').defaultTo(this.db.fn.now());
          table.foreign('stream_id').references('streams.id');
        });
      }

      // Create metrics table if it doesn't exist
      const metricsExists = await this.db.schema.hasTable('metrics');
      if (!metricsExists) {
        await this.db.schema.createTable('metrics', table => {
          table.increments('id').primary();
          table.string('metric_type').notNullable();
          table.float('value').notNullable();
          table.json('metadata').nullable();
          table.timestamp('created_at').defaultTo(this.db.fn.now());
        });
      }

      Logger.info('Database tables created successfully');
    } catch (error) {
      const customError = ErrorHandler.createCustomError(
        'DatabaseError',
        'Failed to create database tables',
        { originalError: error.message }
      );
      throw customError;
    }
  }

  async dropTables() {
    try {
      await this.db.schema.dropTableIfExists('agent_events');
      await this.db.schema.dropTableIfExists('stream_events');
      await this.db.schema.dropTableIfExists('metrics');
      await this.db.schema.dropTableIfExists('agents');
      await this.db.schema.dropTableIfExists('streams');
      Logger.info('Database tables dropped successfully');
    } catch (error) {
      const customError = ErrorHandler.createCustomError(
        'DatabaseError',
        'Failed to drop database tables',
        { originalError: error.message }
      );
      throw customError;
    }
  }

  async resetDatabase() {
    await this.dropTables();
    await this.createTables();
  }

  // Agent operations
  async createAgent(agentId, status = 'idle', nodeId = null) {
    try {
      await this.db('agents').insert({
        id: agentId,
        status,
        node_id: nodeId,
        created_at: new Date(),
        updated_at: new Date()
      });
      Logger.info('Agent created successfully', { agentId, status, nodeId });
    } catch (error) {
      Logger.error('Failed to create agent in database', {
        agentId,
        error: error.message
      });
      throw ErrorHandler.createCustomError(
        'DatabaseError',
        'Failed to create agent',
        { agentId, originalError: error.message }
      );
    }
  }

  async updateAgentStatus(agentId, status, streamId = null) {
    try {
      await this.db('agents')
        .where({ id: agentId })
        .update({
          status,
          stream_id: streamId,
          updated_at: new Date()
        });
      Logger.info('Agent status updated', { agentId, status, streamId });
    } catch (error) {
      throw ErrorHandler.createCustomError(
        'DatabaseError',
        'Failed to update agent status',
        { agentId, status, streamId, originalError: error.message }
      );
    }
  }

  // Stream operations
  async createStream(streamId, channelName, gameId, viewerCount, startedAt) {
    try {
      await this.db('streams').insert({
        id: streamId,
        channel_name: channelName,
        game_id: gameId,
        viewer_count: viewerCount,
        started_at: startedAt,
        created_at: new Date(),
        updated_at: new Date()
      });
      Logger.info('Stream created', { streamId, channelName, gameId });
    } catch (error) {
      throw ErrorHandler.createCustomError(
        'DatabaseError',
        'Failed to create stream',
        { streamId, originalError: error.message }
      );
    }
  }

  async updateStreamViewerCount(streamId, viewerCount) {
    try {
      await this.db('streams')
        .where({ id: streamId })
        .update({
          viewer_count: viewerCount,
          updated_at: new Date()
        });
      Logger.info('Stream viewer count updated', { streamId, viewerCount });
    } catch (error) {
      throw ErrorHandler.createCustomError(
        'DatabaseError',
        'Failed to update stream viewer count',
        { streamId, viewerCount, originalError: error.message }
      );
    }
  }

  // Event logging
  async logAgentEvent(agentId, eventType, eventData = null) {
    try {
      await this.db('agent_events').insert({
        agent_id: agentId,
        event_type: eventType,
        event_data: eventData ? JSON.stringify(eventData) : null,
        created_at: new Date()
      });
      Logger.info('Agent event logged', { agentId, eventType });
    } catch (error) {
      throw ErrorHandler.createCustomError(
        'DatabaseError',
        'Failed to log agent event',
        { agentId, eventType, originalError: error.message }
      );
    }
  }

  async logStreamEvent(streamId, eventType, eventData = null) {
    try {
      await this.db('stream_events').insert({
        stream_id: streamId,
        event_type: eventType,
        event_data: eventData ? JSON.stringify(eventData) : null,
        created_at: new Date()
      });
      Logger.info('Stream event logged', { streamId, eventType });
    } catch (error) {
      throw ErrorHandler.createCustomError(
        'DatabaseError',
        'Failed to log stream event',
        { streamId, eventType, originalError: error.message }
      );
    }
  }

  // Metrics
  async recordMetric(metricType, value, metadata = null) {
    try {
      await this.db('metrics').insert({
        metric_type: metricType,
        value,
        metadata: metadata ? JSON.stringify(metadata) : null,
        created_at: new Date()
      });
      Logger.debug('Metric recorded', { metricType, value });
    } catch (error) {
      throw ErrorHandler.createCustomError(
        'DatabaseError',
        'Failed to record metric',
        { metricType, value, originalError: error.message }
      );
    }
  }

  // Queries
  async getAgentCount() {
    try {
      const result = await this.db('agents').count('* as count').first();
      return result.count;
    } catch (error) {
      throw ErrorHandler.createCustomError(
        'DatabaseError',
        'Failed to get agent count',
        { originalError: error.message }
      );
    }
  }

  async getActiveStreams() {
    try {
      return await this.db('streams')
        .select('*')
        .where('updated_at', '>', this.db.raw("datetime('now', '-1 hour')"));
    } catch (error) {
      throw ErrorHandler.createCustomError(
        'DatabaseError',
        'Failed to get active streams',
        { originalError: error.message }
      );
    }
  }

  async getAgentsByStatus(status) {
    try {
      return await this.db('agents')
        .select('*')
        .where({ status });
    } catch (error) {
      throw ErrorHandler.createCustomError(
        'DatabaseError',
        'Failed to get agents by status',
        { status, originalError: error.message }
      );
    }
  }

  async getAgentHistory(agentId, limit = 100) {
    try {
      return await this.db('agent_events')
        .select('*')
        .where({ agent_id: agentId })
        .orderBy('created_at', 'desc')
        .limit(limit);
    } catch (error) {
      throw ErrorHandler.createCustomError(
        'DatabaseError',
        'Failed to get agent history',
        { agentId, originalError: error.message }
      );
    }
  }

  async getStreamHistory(streamId, limit = 100) {
    try {
      return await this.db('stream_events')
        .select('*')
        .where({ stream_id: streamId })
        .orderBy('created_at', 'desc')
        .limit(limit);
    } catch (error) {
      throw ErrorHandler.createCustomError(
        'DatabaseError',
        'Failed to get stream history',
        { streamId, originalError: error.message }
      );
    }
  }

  async getMetrics(metricType, timeRange = '1 hour') {
    try {
      return await this.db('metrics')
        .select('*')
        .where('metric_type', metricType)
        .where('created_at', '>', this.db.raw(`datetime('now', '-${timeRange}')`))
        .orderBy('created_at', 'desc');
    } catch (error) {
      throw ErrorHandler.createCustomError(
        'DatabaseError',
        'Failed to get metrics',
        { metricType, timeRange, originalError: error.message }
      );
    }
  }

  // Cleanup
  async cleanup() {
    try {
      await this.db.destroy();
      Logger.info('Database connection closed');
    } catch (error) {
      throw ErrorHandler.createCustomError(
        'DatabaseError',
        'Failed to cleanup database connection',
        { originalError: error.message }
      );
    }
  }
}

// Export a singleton instance
module.exports = new DatabaseManager(); 