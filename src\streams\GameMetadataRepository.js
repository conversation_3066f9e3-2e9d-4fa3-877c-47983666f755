const fs = require('fs').promises;
const path = require('path');
const Logger = require('../utils/Logger');

class GameMetadataRepository {
  constructor() {
    this.games = new Map();
    this.streamers = new Map();
    this.categoryIds = new Map();
    this.priorityStreamers = new Set();

    // Cache settings
    this.cacheTimeout = parseInt(process.env.METADATA_CACHE_TIMEOUT || '3600000'); // 1 hour
    this.lastRefresh = null;
    
    // File paths
    this.gamesPath = path.join(__dirname, '../../config/rpg_games.json');
    this.streamersPath = path.join(__dirname, '../../config/rpg_streamers.json');
  }

  async initialize() {
    try {
      Logger.info('Initializing Game Metadata Repository');
      await this.loadGameData();
      await this.loadStreamerData();
      this.lastRefresh = Date.now();
      Logger.info('Game Metadata Repository initialized successfully');
    } catch (error) {
      Logger.error('Failed to initialize Game Metadata Repository', { error: error.message });
      throw error;
    }
  }

  async loadGameData() {
    try {
      const data = await fs.readFile(this.gamesPath, 'utf8');
      const gamesData = JSON.parse(data);

      // Process top RPGs
      for (const game of gamesData.categories.top_rpgs.games) {
        this.games.set(game.name, {
          ...game,
          type: 'video_game'
        });
        this.categoryIds.set(game.category_id, game.name);
      }

      // Process tabletop RPGs
      for (const game of gamesData.categories.tabletop_rpgs.games) {
        this.games.set(game.name, {
          ...game,
          type: 'tabletop'
        });
        this.categoryIds.set(game.category_id, game.name);
      }

      Logger.info('Loaded game metadata', {
        gameCount: this.games.size,
        categoryCount: this.categoryIds.size
      });
    } catch (error) {
      Logger.error('Failed to load game data', { error: error.message });
      throw error;
    }
  }

  async loadStreamerData() {
    try {
      const data = await fs.readFile(this.streamersPath, 'utf8');
      const streamersData = JSON.parse(data);

      // Process tabletop streamers
      for (const streamer of streamersData.categories.tabletop.streamers) {
        this.streamers.set(streamer.channel, {
          ...streamer,
          category: 'tabletop'
        });
        if (streamer.priority <= 2) {
          this.priorityStreamers.add(streamer.channel);
        }
      }

      // Process video game streamers
      for (const streamer of streamersData.categories.video_games.streamers) {
        this.streamers.set(streamer.channel, {
          ...streamer,
          category: 'video_games'
        });
        if (streamer.priority <= 2) {
          this.priorityStreamers.add(streamer.channel);
        }
      }

      Logger.info('Loaded streamer metadata', {
        streamerCount: this.streamers.size,
        priorityStreamerCount: this.priorityStreamers.size
      });
    } catch (error) {
      Logger.error('Failed to load streamer data', { error: error.message });
      throw error;
    }
  }

  async refreshIfNeeded() {
    if (!this.lastRefresh || Date.now() - this.lastRefresh > this.cacheTimeout) {
      await this.initialize();
    }
  }

  async getGameById(categoryId) {
    await this.refreshIfNeeded();
    const gameName = this.categoryIds.get(categoryId);
    return gameName ? this.games.get(gameName) : null;
  }

  async getRpgGameIds() {
    await this.refreshIfNeeded();
    return Array.from(this.categoryIds.keys());
  }

  async getGameScore(categoryId) {
    await this.refreshIfNeeded();
    const game = await this.getGameById(categoryId);
    if (!game) return 1.0;

    // Base score from priority level
    const priorityScores = {
      1: 2.0,  // Top priority
      2: 1.5,  // High priority
      3: 1.2,  // Medium priority
      4: 1.0   // Low priority
    };

    let score = priorityScores[game.priority] || 1.0;

    // Bonus for specific game types
    if (game.type === 'tabletop' && game.name === 'Dungeons & Dragons') {
      score *= 1.2; // D&D bonus
    } else if (game.tags.includes('roleplay')) {
      score *= 1.15; // Roleplay bonus
    }

    return score;
  }

  async isPriorityStreamer(channelName) {
    await this.refreshIfNeeded();
    return this.priorityStreamers.has(channelName.toLowerCase());
  }

  async getStreamerInfo(channelName) {
    await this.refreshIfNeeded();
    return this.streamers.get(channelName.toLowerCase()) || null;
  }

  async getGamesByTag(tag) {
    await this.refreshIfNeeded();
    return Array.from(this.games.values())
      .filter(game => game.tags.includes(tag))
      .sort((a, b) => a.priority - b.priority);
  }

  async getGamesByType(type) {
    await this.refreshIfNeeded();
    return Array.from(this.games.values())
      .filter(game => game.type === type)
      .sort((a, b) => a.priority - b.priority);
  }

  cleanup() {
    this.games.clear();
    this.streamers.clear();
    this.categoryIds.clear();
    this.priorityStreamers.clear();
    this.lastRefresh = null;
    Logger.info('Game Metadata Repository cleaned up');
  }
}

module.exports = GameMetadataRepository; 