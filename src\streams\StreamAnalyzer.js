const GeminiClient = require('../gemini/GeminiClient');
const PromptTemplates = require('../gemini/PromptTemplates');
const Logger = require('../utils/Logger');
const ErrorHandler = require('../utils/ErrorHandler');
const OpenAI = require('openai');

class StreamAnalyzer {
  constructor() {
    this.gemini = new GeminiClient();
    this.analysisCache = new Map();
    this.cacheTimeout = parseInt(process.env.ANALYSIS_CACHE_TIMEOUT || '300000'); // 5 minutes
    this.batchSize = parseInt(process.env.ANALYSIS_BATCH_SIZE || '5');
    this.minConfidence = parseFloat(process.env.ANALYSIS_MIN_CONFIDENCE || '0.7');

    // Configuration
    this.apiKey = ConfigManager.getRequired('GEMINI_API_KEY');
    this.baseUrl = ConfigManager.get('GEMINI_API_BASE_URL', 'https://generativelanguage.googleapis.com/v1beta/openai/');
    this.model = ConfigManager.get('GEMINI_MODEL', 'gemini-2.0-flash');
    this.maxTokens = parseInt(ConfigManager.get('GEMINI_MAX_TOKENS', '1024'));
    this.temperature = parseFloat(ConfigManager.get('GEMINI_TEMPERATURE', '0.7'));
    
    // Initialize Gemini client
    this.client = new OpenAI({
      apiKey: this.apiKey,
      baseURL: this.baseUrl
    });
  }

  async initialize() {
    try {
      await this.gemini.initialize();
      Logger.info('Stream analyzer initialized');
    } catch (error) {
      Logger.error('Failed to initialize stream analyzer', { error: error.message });
      throw error;
    }
  }

  async analyzeStream(stream, gameMetadata, context) {
    try {
      // Check cache first
      const cachedAnalysis = this.getCachedAnalysis(stream.id);
      if (cachedAnalysis) {
        return cachedAnalysis;
      }

      // Get appropriate template
      const template = PromptTemplates.getTemplateForGame(gameMetadata);

      // Prepare context
      const promptContext = {
        gameName: gameMetadata.name,
        gameType: gameMetadata.type,
        streamerName: stream.userName,
        ...context
      };

      // Add game-specific context
      if (gameMetadata.type === 'tabletop') {
        promptContext.showName = stream.title.split(' - ')[0];
        promptContext.campaignName = stream.title.split(' - ')[1];
        promptContext.dmName = stream.userName;
      } else if (gameMetadata.category_id === '32982') { // GTA V
        promptContext.serverName = this.extractServerName(stream.title);
        promptContext.characterName = this.extractCharacterName(stream.title);
      }

      // Fill template
      const filledTemplate = PromptTemplates.fillTemplate(template, promptContext);

      // Generate analysis
      const result = await this.gemini.generateContent(filledTemplate.userPrompt, {
        systemPrompt: filledTemplate.system
      });

      // Parse response
      const analysis = this.parseAnalysisResponse(result.text);

      // Cache result if confidence is high enough
      if (analysis.confidenceScore >= this.minConfidence * 100) {
        this.cacheAnalysis(stream.id, analysis);
      }

      return analysis;
    } catch (error) {
      Logger.error('Failed to analyze stream', {
        streamId: stream.id,
        error: error.message
      });
      return null;
    }
  }

  async analyzeBatch(streams, gameMetadataMap, contextMap) {
    const results = new Map();
    const batches = this.createBatches(streams, this.batchSize);

    for (const batch of batches) {
      const batchPromises = batch.map(stream => {
        const gameMetadata = gameMetadataMap.get(stream.gameId);
        const context = contextMap.get(stream.id);
        return this.analyzeStream(stream, gameMetadata, context);
      });

      try {
        const batchResults = await Promise.all(batchPromises);
        batch.forEach((stream, index) => {
          if (batchResults[index]) {
            results.set(stream.id, batchResults[index]);
          }
        });
      } catch (error) {
        Logger.error('Failed to analyze batch', { error: error.message });
      }
    }

    return results;
  }

  parseAnalysisResponse(responseText) {
    try {
      // Remove any non-JSON text before/after the JSON object
      const jsonStart = responseText.indexOf('{');
      const jsonEnd = responseText.lastIndexOf('}') + 1;
      const jsonStr = responseText.slice(jsonStart, jsonEnd);
      
      return JSON.parse(jsonStr);
    } catch (error) {
      Logger.error('Failed to parse analysis response', {
        response: responseText,
        error: error.message
      });
      return null;
    }
  }

  extractServerName(title) {
    // Common GTA RP server patterns
    const serverPatterns = [
      /\[(.*?)\]/,                    // [ServerName]
      /\((.*?)\)/,                    // (ServerName)
      /(?:NoPixel|TwitchRP|Eclipse)/, // Known server names
      /\| (.*?) \|/                   // | ServerName |
    ];

    for (const pattern of serverPatterns) {
      const match = title.match(pattern);
      if (match) {
        return match[1] || match[0];
      }
    }

    return 'Unknown Server';
  }

  extractCharacterName(title) {
    // Common character name patterns
    const namePatterns = [
      /(?:playing|as) ([^|[\]()]+)/i,  // "playing as CharacterName" or "as CharacterName"
      /([^|[\]()]+?) (?:in|on|@)/i,    // "CharacterName in/on/@ ..."
      /^([^|[\]()]+?) [-|]/            // "CharacterName -" or "CharacterName |"
    ];

    for (const pattern of namePatterns) {
      const match = title.match(pattern);
      if (match) {
        return match[1].trim();
      }
    }

    return 'Unknown Character';
  }

  createBatches(items, batchSize) {
    const batches = [];
    for (let i = 0; i < items.length; i += batchSize) {
      batches.push(items.slice(i, i + batchSize));
    }
    return batches;
  }

  getCachedAnalysis(streamId) {
    const cached = this.analysisCache.get(streamId);
    if (cached && (Date.now() - cached.timestamp) < this.cacheTimeout) {
      return cached.analysis;
    }
    return null;
  }

  cacheAnalysis(streamId, analysis) {
    this.analysisCache.set(streamId, {
      analysis,
      timestamp: Date.now()
    });
  }

  clearCache() {
    this.analysisCache.clear();
  }

  cleanup() {
    this.clearCache();
    this.gemini.cleanup();
    Logger.info('Stream analyzer cleanup completed');
  }
}

module.exports = StreamAnalyzer; 