const TwitchApiClient = require('./TwitchApiClient');
const GameMetadataRepository = require('./GameMetadataRepository');
const StreamAnalyzer = require('./StreamAnalyzer');
const DatabaseManager = require('../database/DatabaseManager');
const Logger = require('../utils/Logger');

class StreamManager {
  constructor() {
    this.twitchClient = new TwitchApiClient();
    this.gameMetadata = new GameMetadataRepository();

    // Configuration
    this.refreshInterval = parseInt(process.env.STREAM_REFRESH_INTERVAL || '300000'); // 5 minutes
    this.minViewerCount = parseInt(process.env.MIN_VIEWER_COUNT || '50');
    this.maxConcurrentStreams = parseInt(process.env.MAX_CONCURRENT_STREAMS || '30');
    this.priorityWeight = parseFloat(process.env.PRIORITY_STREAMER_WEIGHT || '1.5');
    this.analysisWeight = parseFloat(process.env.ANALYSIS_WEIGHT || '2.0');
    this.rareContentBonus = parseFloat(process.env.RARE_CONTENT_BONUS || '1.25');
    
    // Cache for active streams
    this.activeStreams = new Map();
    this.streamScores = new Map();
    this.categoryCache = new Map();
    
    // Start refresh loop
    this.startRefreshLoop();
  }

  async initialize() {
    try {
      Logger.info('Initializing Stream Manager');
      
      // Load game metadata
      await this.gameMetadata.initialize();
      
      // Initialize Twitch client
      await this.twitchClient.initialize();
      
      // Load existing streams from database
      await this.loadExistingStreams();
      
      Logger.info('Stream Manager initialized successfully');
    } catch (error) {
      Logger.error('Failed to initialize Stream Manager', { error: error.message });
      throw error;
    }
  }

  async loadExistingStreams() {
    try {
      const streams = await DatabaseManager.getActiveStreams();
      streams.forEach(stream => {
        this.activeStreams.set(stream.id, {
          id: stream.id,
          title: stream.title,
          gameId: stream.game_id,
          viewerCount: stream.viewer_count,
          startedAt: new Date(stream.started_at),
          priority: stream.priority,
          lastUpdated: new Date(stream.updated_at)
        });
      });
      Logger.info('Loaded existing streams', { count: streams.length });
    } catch (error) {
      Logger.error('Failed to load existing streams', { error: error.message });
      throw error;
    }
  }

  startRefreshLoop() {
    this.refreshTimer = setInterval(async () => {
      try {
        await this.refreshStreams();
      } catch (error) {
        Logger.error('Stream refresh failed', { error: error.message });
      }
    }, this.refreshInterval);
  }

  async refreshStreams() {
    try {
      Logger.info('Starting stream refresh');
      
      // Get current RPG streams
      const rpgStreams = await this.discoverRpgStreams();
      
      // Get game metadata for all streams
      const gameMetadataMap = new Map();
      for (const stream of rpgStreams) {
        const metadata = await this.gameMetadata.getGameById(stream.gameId);
        if (metadata) {
          gameMetadataMap.set(stream.gameId, metadata);
        }
      }
      
      // Get stream analysis in batches
      const { results: analysisResults } = await StreamAnalyzer.analyzeStreamBatch(
        rpgStreams,
        gameMetadataMap
      );
      
      // Update stream scores with analysis results
      await this.updateStreamScores(rpgStreams, analysisResults);
      
      // Update active streams
      await this.updateActiveStreams();
      
      // Clean up ended streams
      await this.cleanupEndedStreams();
      
      Logger.info('Stream refresh completed', {
        activeStreams: this.activeStreams.size,
        discoveredStreams: rpgStreams.length,
        analyzedStreams: analysisResults.size
      });
    } catch (error) {
      Logger.error('Stream refresh failed', { error: error.message });
      throw error;
    }
  }

  async discoverRpgStreams() {
    try {
      // Get list of RPG game IDs from metadata repository
      const rpgGameIds = await this.gameMetadata.getRpgGameIds();
      
      // Fetch streams for each game
      const streams = [];
      for (const gameId of rpgGameIds) {
        const gameStreams = await this.twitchClient.getStreamsByGame(gameId);
        streams.push(...gameStreams);
      }
      
      // Filter by minimum viewer count
      return streams.filter(stream => stream.viewerCount >= this.minViewerCount);
    } catch (error) {
      Logger.error('Failed to discover RPG streams', { error: error.message });
      throw error;
    }
  }

  async updateStreamScores(streams, analysisResults) {
    for (const stream of streams) {
      try {
        // Get base score from viewer count
        let score = stream.viewerCount;
        
        // Get game metadata and score
        const gameMetadata = await this.gameMetadata.getGameById(stream.gameId);
        const gameScore = await this.gameMetadata.getGameScore(stream.gameId);
        score *= gameScore;

        // Get streamer info and apply priority multiplier
        const streamerInfo = await this.gameMetadata.getStreamerInfo(stream.userId);
        const isPriority = await this.gameMetadata.isPriorityStreamer(stream.userId);
        
        if (isPriority) {
          score *= this.priorityWeight;
          
          // Additional boost for streamers playing their preferred games
          if (streamerInfo && streamerInfo.games && streamerInfo.games.includes(gameMetadata?.name)) {
            score *= 1.1; // 10% boost for playing preferred game
          }
        }

        // Apply analysis-based scoring
        const analysis = analysisResults.get(stream.id);
        if (analysis) {
          // Apply relevance score
          score *= (1 + analysis.relevanceScore * this.analysisWeight);

          // Boost for gameplay content
          if (analysis.contentType === 'gameplay') {
            score *= 1.2;
          }

          // Boost for rare content
          if (analysis.hasRareContent) {
            score *= this.rareContentBonus;
          }

          // Game-specific content boosts
          if (gameMetadata) {
            // Boost for tabletop RPG storytelling moments
            if (gameMetadata.type === 'tabletop' && analysis.contentType === 'storytelling') {
              score *= 1.25;
            }
            
            // Boost for roleplay content in games with roleplay tag
            if (gameMetadata.tags.includes('roleplay') && analysis.contentType === 'roleplay') {
              score *= 1.2;
            }
          }

          // Confidence adjustment
          score *= analysis.confidence;
        }
        
        // Store score and metadata
        this.streamScores.set(stream.id, {
          score,
          isPriority,
          gameScore,
          streamerInfo: streamerInfo || null,
          gameMetadata: gameMetadata || null,
          analysis: analysis || null,
          timestamp: new Date()
        });
      } catch (error) {
        Logger.error('Failed to update stream score', {
          streamId: stream.id,
          error: error.message
        });
      }
    }
  }

  async updateActiveStreams() {
    // Sort streams by score
    const sortedStreams = Array.from(this.streamScores.entries())
      .sort(([, a], [, b]) => b.score - a.score)
      .slice(0, this.maxConcurrentStreams);
    
    for (const [streamId, scoreData] of sortedStreams) {
      try {
        // Get full stream data
        const streamData = await this.twitchClient.getStreamById(streamId);
        if (!streamData) continue;
        
        // Update or add to active streams
        this.activeStreams.set(streamId, {
          ...streamData,
          score: scoreData.score,
          isPriority: scoreData.isPriority,
          analysis: scoreData.analysis,
          lastUpdated: new Date()
        });
        
        // Update database
        await DatabaseManager.updateStream(streamId, {
          title: streamData.title,
          gameId: streamData.gameId,
          viewerCount: streamData.viewerCount,
          priority: scoreData.isPriority,
          score: scoreData.score,
          contentType: scoreData.analysis?.contentType || null,
          relevanceScore: scoreData.analysis?.relevanceScore || null,
          hasRareContent: scoreData.analysis?.hasRareContent || false,
          estimatedLevel: scoreData.analysis?.estimatedLevel || null
        });
      } catch (error) {
        Logger.error('Failed to update active stream', {
          streamId,
          error: error.message
        });
      }
    }
  }

  async cleanupEndedStreams() {
    const now = new Date();
    const endedStreams = [];
    
    for (const [streamId, streamData] of this.activeStreams) {
      // Check if stream is still in top scores
      if (!this.streamScores.has(streamId)) {
        endedStreams.push(streamId);
        continue;
      }
      
      // Check if stream is still live
      try {
        const isLive = await this.twitchClient.isStreamLive(streamId);
        if (!isLive) {
          endedStreams.push(streamId);
        }
      } catch (error) {
        Logger.error('Failed to check stream status', {
          streamId,
          error: error.message
        });
      }
    }
    
    // Remove ended streams
    for (const streamId of endedStreams) {
      this.activeStreams.delete(streamId);
      await DatabaseManager.markStreamEnded(streamId);
    }
    
    if (endedStreams.length > 0) {
      Logger.info('Cleaned up ended streams', { count: endedStreams.length });
    }
  }

  getActiveStreams() {
    return Array.from(this.activeStreams.values());
  }

  async getStreamById(streamId) {
    return this.activeStreams.get(streamId) || null;
  }

  async getRecommendedStreamForAgent() {
    // Get streams not at capacity
    const availableStreams = Array.from(this.activeStreams.values())
      .filter(stream => {
        const agentCount = stream.currentAgents || 0;
        const maxAgents = Math.ceil(stream.viewerCount * 0.05); // 5% of viewers
        
        // Additional filtering based on analysis
        const analysis = stream.analysis;
        if (analysis) {
          // Prefer gameplay content
          if (analysis.contentType !== 'gameplay') return false;
          
          // Require minimum relevance
          if (analysis.relevanceScore < 0.6) return false;
          
          // Require minimum confidence
          if (analysis.confidence < 0.7) return false;
        }
        
        return agentCount < maxAgents;
      })
      .sort((a, b) => {
        // Prioritize streams with rare content
        if (a.analysis?.hasRareContent && !b.analysis?.hasRareContent) return -1;
        if (!a.analysis?.hasRareContent && b.analysis?.hasRareContent) return 1;
        
        // Then sort by score
        return b.score - a.score;
      });
    
    return availableStreams[0] || null;
  }

  cleanup() {
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer);
    }
    this.activeStreams.clear();
    this.streamScores.clear();
    this.categoryCache.clear();
    Logger.info('Stream Manager cleanup completed');
  }
}

module.exports = new StreamManager(); 