const axios = require('axios');
const Logger = require('../utils/Logger');
const ErrorHandler = require('../utils/ErrorHandler');

class TwitchApiClient {
  constructor() {
    this.clientId = process.env.TWITCH_CLIENT_ID;
    this.clientSecret = process.env.TWITCH_CLIENT_SECRET;
    this.baseUrl = 'https://api.twitch.tv/helix';
    this.accessToken = null;
    this.tokenExpiry = null;
    this.isConfigured = !!(this.clientId && this.clientSecret);

    if (!this.isConfigured) {
      Logger.warn('Twitch API credentials not configured. Twitch API features will be disabled.');
    }
  }

  async initialize() {
    if (!this.isConfigured) {
      Logger.warn('Skipping Twitch API Client initialization - credentials not configured');
      return;
    }

    try {
      Logger.info('Initializing Twitch API Client');
      await this.authenticate();
      Logger.info('Twitch API Client initialized successfully');
    } catch (error) {
      Logger.error('Failed to initialize Twitch API Client', { error: error.message });
      throw error;
    }
  }

  async authenticate() {
    try {
      const response = await axios.post('https://id.twitch.tv/oauth2/token', null, {
        params: {
          client_id: this.clientId,
          client_secret: this.clientSecret,
          grant_type: 'client_credentials'
        }
      });

      this.accessToken = response.data.access_token;
      this.tokenExpiry = Date.now() + (response.data.expires_in * 1000);

      Logger.info('Twitch authentication successful');
    } catch (error) {
      throw ErrorHandler.createCustomError(
        'TwitchApiError',
        'Authentication failed',
        { originalError: error.message }
      );
    }
  }

  async ensureValidToken() {
    if (!this.accessToken || Date.now() >= this.tokenExpiry - 300000) { // Refresh 5 minutes before expiry
      await this.authenticate();
    }
  }

  async makeRequest(endpoint, params = {}) {
    if (!this.isConfigured) {
      throw new Error('Twitch API client not configured - missing credentials');
    }

    try {
      await this.ensureValidToken();

      const response = await axios.get(`${this.baseUrl}${endpoint}`, {
        headers: {
          'Client-ID': this.clientId,
          'Authorization': `Bearer ${this.accessToken}`
        },
        params
      });

      return response.data;
    } catch (error) {
      if (error.response?.status === 401) {
        // Token might be invalid, try to re-authenticate once
        await this.authenticate();
        
        // Retry the request
        const response = await axios.get(`${this.baseUrl}${endpoint}`, {
          headers: {
            'Client-ID': this.clientId,
            'Authorization': `Bearer ${this.accessToken}`
          },
          params
        });

        return response.data;
      }

      throw ErrorHandler.createCustomError(
        'TwitchApiError',
        'API request failed',
        { endpoint, params, originalError: error.message }
      );
    }
  }

  async getStreamsByGame(gameId, first = 100) {
    try {
      const data = await this.makeRequest('/streams', {
        game_id: gameId,
        first
      });

      return data.data.map(stream => ({
        id: stream.id,
        userId: stream.user_id,
        userName: stream.user_name,
        gameId: stream.game_id,
        title: stream.title,
        viewerCount: stream.viewer_count,
        startedAt: new Date(stream.started_at),
        language: stream.language,
        thumbnailUrl: stream.thumbnail_url
      }));
    } catch (error) {
      Logger.error('Failed to get streams by game', {
        gameId,
        error: error.message
      });
      throw error;
    }
  }

  async getStreamById(streamId) {
    try {
      const data = await this.makeRequest('/streams', {
        user_id: streamId
      });

      if (data.data.length === 0) {
        return null;
      }

      const stream = data.data[0];
      return {
        id: stream.id,
        userId: stream.user_id,
        userName: stream.user_name,
        gameId: stream.game_id,
        title: stream.title,
        viewerCount: stream.viewer_count,
        startedAt: new Date(stream.started_at),
        language: stream.language,
        thumbnailUrl: stream.thumbnail_url
      };
    } catch (error) {
      Logger.error('Failed to get stream by ID', {
        streamId,
        error: error.message
      });
      throw error;
    }
  }

  async isStreamLive(streamId) {
    try {
      const stream = await this.getStreamById(streamId);
      return stream !== null;
    } catch (error) {
      Logger.error('Failed to check stream status', {
        streamId,
        error: error.message
      });
      throw error;
    }
  }

  async getGameById(gameId) {
    try {
      const data = await this.makeRequest('/games', {
        id: gameId
      });

      if (data.data.length === 0) {
        return null;
      }

      const game = data.data[0];
      return {
        id: game.id,
        name: game.name,
        boxArtUrl: game.box_art_url
      };
    } catch (error) {
      Logger.error('Failed to get game by ID', {
        gameId,
        error: error.message
      });
      throw error;
    }
  }

  async searchCategories(query, first = 20) {
    try {
      const data = await this.makeRequest('/search/categories', {
        query,
        first
      });

      return data.data.map(category => ({
        id: category.id,
        name: category.name,
        boxArtUrl: category.box_art_url
      }));
    } catch (error) {
      Logger.error('Failed to search categories', {
        query,
        error: error.message
      });
      throw error;
    }
  }

  async getStreamTags(streamId) {
    try {
      const data = await this.makeRequest('/streams/tags', {
        broadcaster_id: streamId
      });

      return data.data.map(tag => ({
        id: tag.tag_id,
        name: tag.localization_names['en-us'],
        description: tag.localization_descriptions['en-us']
      }));
    } catch (error) {
      Logger.error('Failed to get stream tags', {
        streamId,
        error: error.message
      });
      throw error;
    }
  }
}

module.exports = TwitchApiClient; 