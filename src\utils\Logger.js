const winston = require('winston');
const path = require('path');
const fs = require('fs');

class Logger {
  constructor() {
    this.logDir = path.join(process.cwd(), 'logs');
    this.logFile = path.join(this.logDir, 'fleet-manager.log');
    this.errorFile = path.join(this.logDir, 'error.log');
    this.ensureLogDirectory();
    this.ensureLogFiles();
    this.initializeLogger();
  }

  ensureLogDirectory() {
    if (!fs.existsSync(this.logDir)) {
      fs.mkdirSync(this.logDir, { recursive: true });
    }
  }

  ensureLogFiles() {
    // Create log files if they don't exist
    if (!fs.existsSync(this.logFile)) {
      fs.writeFileSync(this.logFile, '');
    }
    if (!fs.existsSync(this.errorFile)) {
      fs.writeFileSync(this.errorFile, '');
    }
  }

  initializeLogger() {
    const logLevel = process.env.LOG_LEVEL || 'info';

    this.logger = winston.createLogger({
      level: logLevel,
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.errors({ stack: true }),
        winston.format.json()
      ),
      defaultMeta: { service: 'fleet-manager' },
      transports: [
        // Write to all logs with level 'info' and below to combined.log
        new winston.transports.File({
          filename: this.logFile,
          maxsize: 5242880, // 5MB
          maxFiles: 5,
          tailable: true,
          options: { flags: 'w' } // Overwrite file on startup
        }),
        // Write all logs error (and below) to error.log
        new winston.transports.File({
          filename: this.errorFile,
          level: 'error',
          maxsize: 5242880, // 5MB
          maxFiles: 5,
          tailable: true,
          options: { flags: 'w' } // Overwrite file on startup
        })
      ]
    });

    // If we're not in production, log to the console with colors
    if (process.env.NODE_ENV !== 'production') {
      this.logger.add(new winston.transports.Console({
        format: winston.format.combine(
          winston.format.colorize(),
          winston.format.simple()
        )
      }));
    }
  }

  log(level, message, meta = {}) {
    this.logger.log(level, message, meta);
    // For testing purposes, write synchronously
    if (process.env.NODE_ENV === 'test') {
      this.writeSync(level, message, meta);
    }
  }

  info(message, meta = {}) {
    this.log('info', message, meta);
  }

  error(message, meta = {}) {
    this.log('error', message, meta);
  }

  warn(message, meta = {}) {
    this.log('warn', message, meta);
  }

  debug(message, meta = {}) {
    this.log('debug', message, meta);
  }

  // Log agent-specific events
  logAgentEvent(agentId, event, meta = {}) {
    this.info(`Agent ${agentId}: ${event}`, { ...meta, agentId, eventType: event });
  }

  // Log stream-related events
  logStreamEvent(streamId, event, meta = {}) {
    this.info(`Stream ${streamId}: ${event}`, { ...meta, streamId, eventType: event });
  }

  // Log system events
  logSystemEvent(event, meta = {}) {
    this.info(`System: ${event}`, { ...meta, eventType: event });
  }

  // Log performance metrics
  logMetrics(metrics) {
    this.debug('Performance metrics', { metrics });
  }

  // Get log file paths for testing
  getLogFilePaths() {
    return {
      logFile: this.logFile,
      errorFile: this.errorFile
    };
  }

  // Synchronous write for testing
  writeSync(level, message, meta = {}) {
    const logEntry = JSON.stringify({
      level,
      message,
      ...meta,
      timestamp: new Date().toISOString(),
      service: 'fleet-manager'
    }) + '\n';

    fs.appendFileSync(this.logFile, logEntry);
    if (level === 'error') {
      fs.appendFileSync(this.errorFile, logEntry);
    }
  }
}

// Export a singleton instance
module.exports = new Logger(); 